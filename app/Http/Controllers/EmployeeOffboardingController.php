<?php

namespace App\Http\Controllers;

use App\Models\EmployeeOffboarding;
use App\Models\Plaatsing;
use App\Services\CommunicationService;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EmployeeOffboardingController extends Controller
{
    private $easyflex;
    private $communicationService;


    public function __construct()
    {
        $this->easyflex = new EasyflexService();
        $this->communicatie = new CommunicationService();
    }
    public function offboardUser($data, $applicant)
    {
        // get all plaatsingen
        $plaatsingen = Plaatsing::where('applicant_id', $applicant->applicant_id)
            ->whereNull('offboard_date')
            ->get();

        $plaatsingIds = [];
        if(!$plaatsingen->isEmpty()){
            $plaatsingIds = $plaatsingen->pluck('plaatsing_id')->toArray();
        }

        // save offboarding in db
        $offboarding = new EmployeeOffboarding();
        $offboarding->applicant_id = $applicant->applicant_id;
        $offboarding->reason = $data['reason'];
        $offboarding->offboarding_date = $data['end_contract_per_date'];
        $offboarding->plaatsing_ids = $plaatsingIds;
        $offboarding->contract_id = $data['contract_number'];
        $offboarding->user_id = auth()->user()->id;
        $offboarding->save();

        foreach($plaatsingen as $plaatsing){
            $plaatsing->offboard_date = $data['end_contract_per_date'];
            $plaatsing->update();
        }

        $details = array_merge($data, $applicant->toArray());

        $details['offboard_now'] = false;
        $endContractDate = Carbon::createFromFormat('Y-m-d', $data['end_contract_per_date']);
        if ($endContractDate->isToday() || $endContractDate->isPast()) {
            $details['offboard_now'] = true;
        }
        $details['recruiter_name'] = auth()->user()->name;
        $details['WKM_id'] = 7;
        $details['plaatsing_ids'] = $plaatsingIds;
        $details['registratienummer'] = $applicant->easyflex_id;
        $details['contract_number'] = intval($details['contract_number']);

        // update plaatsing einddatum/aantekening in ef/
        $this->easyflex->offboardUserEasyflex($details);

        // dont send email when skip checkbox is checked
        if(($data['skip_email'] ?? false) === false){
            // send message communicatieservice + download and save pdfs to easyflex
            return $this->communicatie->sendOffboardMessages($details);
        }
        return true;
    }
}
