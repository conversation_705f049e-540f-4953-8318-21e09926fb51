<?php

namespace App\Http\Controllers;

use App\Models\JetApplicant;
use App\Services\FusionauthService;
use App\Services\UserService;
use App\Actions\JetActions;
use App\Actions\JetApplicantEnrollmentActions;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Sleep;

class ContractController extends Controller
{
    public function generateContract($userdata, $formData, $concept = false): bool|JsonResponse|string
    {
        $message = $this->signMessage($userdata->flexappData->applicationProfile->language);

        $language = ($userdata->flexappData->applicationProfile->language === 'Engels')
            ? 'en-US'
            : 'nl-NL';

        $signhostData = $formData;
        $signhostData['is_werkstudent'] = $userdata->flexappData->info->twv_is_student;
        if($formData['contract_type'] === 'contract-wijziging'){
            $signhostData['contract_type'] = 'contract';
        }

        $signhostData['is_import'] = $userdata->is_import;
        $startDate = Carbon::parse($formData['contract_start_date']);
        $probationEndDate = $formData['probation_period'] ? $startDate->addMonth()->format('Y-m-d') : null;

        $jsonBody = [
            "Seal" => false,
            "Signers" => [
                [
                    "Email" => $userdata->flexappData->personalData->email,
                    "Verifications" => [
                        [
                            "Type" => "Scribble",
                            "RequireHandsignature" => true,
                            "ScribbleName" => $userdata->flexappData->personalData->first_name.' '.$userdata->flexappData->personalData->last_name
                        ]
                    ],
                    "SendSignRequest" => true,
                    "SignRequestMessage" => $message['signmessage'],
                    "SignRequestSubject" => $message['subject'],
                    "Language" => $language,
                    "DaysToRemind" => 1,
                    "ReturnUrl" => "https://flexapp.365werk.nl"
                ]
            ],
            "Receivers" => [
                [
                    "Name" => "365werk back-office",
                    "Email" => "<EMAIL>",
                    "Language" => 'nl-NL',
                    "Message" => "Er is een nieuw contract verstuurd naar: ".$userdata->flexappData->personalData->first_name.' '.$userdata->flexappData->personalData->last_name
                ]
            ],
            "Reference" => "JET nieuw contract",
            "PostbackUrl" => null,
            "SignRequestMode" => 2,
            "DaysToExpire" => 10,
            "SendEmailNotifications" => false,
            "AutoGenerateDocuments" => true,
            "Jet" => true,
            "UserId" => $userdata->flexapp_id,
            "FormData" => $signhostData
        ];

        if($concept === true){
            $request = Http::baseUrl(config('services.signhost_service.url'))
                ->withHeaders([
                    'Accept' => 'application/vnd.api+json',
                    'Content-Type' => 'application/vnd.api+json'
                ])
                ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.signhost_service')))
                ->retry(2)
                ->post('/download_concept', $jsonBody);
        } else {
            $userService = new UserService;
            $userService->updateUser($userdata->flexapp_id, [
                'info' => [
                    'start_date' => $formData['contract_start_date'],
                    'end_date' => $formData['contract_end_date'],
                    'minimum_contract_hours' => $formData['contract_hours'],
                    'probation_end_date' => $probationEndDate
                ],
                'financial_details' => [
                    'is_wage_with_vacation_allowance' => $formData['is_wage_with_vacation_allowance'],
                    'is_wage_with_vacation_days' => $formData['is_wage_with_vacation_days']
                ]
            ]);

            $request = Http::baseUrl(config('services.signhost_service.url'))
                ->withHeaders([
                    'Accept' => 'application/vnd.api+json',
                    'Content-Type' => 'application/vnd.api+json'
                ])
                ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.signhost_service')))
                ->retry(2)
                ->post('/create_transaction', $jsonBody);
        }

        if ($request->successful()) {
            if($concept === true){
                $file = $this->downloadConceptContract($request->json());
                Sleep::for(5)->seconds();
                return $file;
            }

            if(is_array($request->json())){
                return false;
            }

            $response = json_decode($request->json());
            if ($response->Id != null && $formData['contract_type'] !== 'renew_contract') {
                $step6StartedAt = JetActions::timeApplicantEnrollmentStep($userdata->applicant_id, 6);
                JetApplicantEnrollmentActions::startStep($userdata->applicant_id, 12, false, $step6StartedAt);
                return true;
            }
            return false;
        } else {
            return false;
        }
    }


    public function downloadConceptContract($contracts):string
    {
        foreach($contracts as $file){
            $decodedFile = base64_decode($file['file']);
            $filelocation = 'temp/'.$file['fileId'];
            Storage::put($filelocation, $decodedFile);
            return $filelocation;
        }
    }

    public function updateContractStatus(Request $request)
    {
        $uuid = $request->uuid;
        $status = $request->status;
        $contractStatus = match (true) {
            $status == 30 => 7,          // signed
            $status == 40 => 8,          // declined
            $status > 40 => 9,           // not signed
            default => null,             // fallback for unknown statuses
        };

        if($contractStatus){
            $applicant = JetApplicant::where('flexapp_id', $uuid)->first();
            JetActions::timeApplicantRenewalStep($applicant->applicant_id, $contractStatus, true);
        }
    }

    public function generateConceptContract($userdata, $formData): bool|JsonResponse
    {
        $message = $this->signMessage($userdata->flexappData->applicationProfile->language);
        $language = ($userdata->flexappData->applicationProfile->language === 'Engels')
            ? 'en-US'
            : 'nl-NL';

        $signhostData = $formData;
        if($formData['contract_type'] === 'contract-wijziging'){
            $signhostData['contract_type'] = 'contract';
        }

        $jsonBody = [
            "Seal" => false,
            "Signers" => [
                [
                    "Email" => $userdata->flexappData->personalData->email,
                    "Verifications" => [
                        [
                            "Type" => "Scribble",
                            "RequireHandsignature" => true,
                            "ScribbleName" => $userdata->flexappData->personalData->first_name.' '.$userdata->flexappData->personalData->last_name
                        ]
                    ],
                    "SendSignRequest" => true,
                    "SignRequestMessage" => $message['signmessage'],
                    "SignRequestSubject" => $message['subject'],
                    "Language" => $language,
                    "DaysToRemind" => 1,
                    "ReturnUrl" => "https://flexapp.365werk.nl"
                ]
            ],
            "Receivers" => [
                [
                    "Name" => "365werk back-office",
                    "Email" => "<EMAIL>",
                    "Language" => 'nl-NL',
                    "Message" => "Er is een nieuw contract verstuurd naar: ".$userdata->flexappData->personalData->first_name.' '.$userdata->flexappData->personalData->last_name
                ]
            ],
            "Reference" => "JET nieuw contract",
            "PostbackUrl" => config('services.signhost_service.postback_url'),
            "SignRequestMode" => 2,
            "DaysToExpire" => 10,
            "SendEmailNotifications" => false,
            "AutoGenerateDocuments" => true,
            "Jet" => true,
            "UserId" => $userdata->flexapp_id,
            "FormData" => $signhostData
        ];

        return Http::baseUrl(config('services.signhost_service.url'))
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json'
            ])
            ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.signhost_service')))
            ->retry(2)
            ->post('/create_transaction', $jsonBody)
            ->json();
    }

    public function signMessage($language): array
    {
        if($language === 'Nederlands'){
            $signmessage = 'Hoi, hierbij ontvang je van ons de documenten om te ondertekenen. Lees deze rustig door en wanneer je akkoord bent kan je ze gemakkelijk en online ondertekenen.';
            $subject = '365Werk heeft je een document gestuurd ter ondertekening';
        }

        if($language === 'Engels'){
            $signmessage = 'Hi, we have sent you the documents to sign. Please take your time to read through them, and once you agree, you can easily sign them online.';
            $subject = "Please sign these documents";
        }

        return ['signmessage' => $signmessage, 'subject' => $subject];
    }
}
