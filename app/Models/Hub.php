<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Hub extends Model
{
    protected $fillable = [
        'location',
        'email_address',
    ];

    /**
     * Get the plaatsingen for the hub based on kpcode matching location
     * Only includes non-deleted plaatsingen with no offboard_date or future offboard_date
     */
    public function plaatsingen(): HasMany
    {
        return $this->hasMany(Plaatsing::class, 'kpcode', 'location')
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->whereNull('offboard_date')
                      ->orWhere('offboard_date', '>', now());
            });
    }
}
