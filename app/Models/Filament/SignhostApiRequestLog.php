<?php

namespace App\Models\Filament;

use App\Models\Filament\Casts\SignhostHeaderCast;
use App\Models\Filament\Casts\SignhostJsonCast;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

/**
 * @mixins Builder
 */
class SignhostApiRequestLog extends Model implements RemoteModel
{
    use <PERSON><PERSON>, SushiBuilder, RemoteModelDefaults;

    public $incrementing = false;

    protected $fillable = [
        'id',
        'api_endpoint',
        'request_endpoint',
        'method',
        'headers',
        'transaction_id',
        'transaction',
        'stage',
        'response_code',
        'response_error_code',
        'response_message',
        'created_at',
        'updated_at',
    ];

    protected function casts(): array
    {
        return [
            'headers' => SignhostHeaderCast::class,
            'transaction' => SignhostJsonCast::class,
            'response_message' => SignhostJsonCast::class,
        ];
    }

    public function getRows()
    {
        return $this->getData($this->query)
            ->toArray();
    }

    /**
     * Temporarily disable <PERSON>shi caching to avoid array-to-string conversion issues
     */
    public function sushiShouldCache(): bool
    {
        return false;
    }

    /**
     * Get fields that require JSON encoding during API data retrieval.
     *
     * Used by SushiBuilder to determine which fields need JSON encoding when
     * processing data from the Signhost API. This method is required for proper
     * data handling during API communication.
     *
     * Note: Field transformations during model instantiation are now handled
     * automatically by custom casts:
     * - 'headers': SignhostHeaderCast (comma-separated key:value parsing)
     * - 'transaction': SignhostJsonCast (standard JSON decode)
     * - 'response_message': SignhostJsonCast (standard JSON decode)
     */
    protected function getJsonFields(): array
    {
        return [
            'headers',
            'transaction',
            'response_message',
        ];
    }

    public function remoteUrl(): string
    {
        return 'logs/signhost_requests';
    }
}
