<?php

namespace App\Models\Filament;

use App\Services\SignhostService;
use GuzzleHttp\Utils;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

/**
 * @mixin Builder
 */
trait SushiBuilder
{
    public array $query = [
        'per_page' => 15,
        'page' => 1,
    ];

    public function setQuery(array $query): self
    {
        $this->query = [
            ...$this->query,
            ...$query,
        ];

        return $this;
    }

    public function newEloquentBuilder($query): Builder
    {
        return new SushiEloquentBuilder($query);
    }

    abstract protected function getJsonFields(): array;

    public function getData(?array $query = [], ?array $fields = null): Collection
    {
        $remoteModelRequest = fn () => (new SignhostService())->modelRequest(
            remoteModel: $this,
            query: [...$this->query, ...$query],
            fields: $fields,
        );

        $cacheKey = array_key_exists($this->getRouteKeyName(), $query)
            ? $this->getSingleRecordCacheKey(data_get($query, $this->getRouteKeyName()))
            : $this->getRecordsCacheKey();

        $logs = $this->sushiShouldCache()
            ? Cache::remember($cacheKey, $this->getSingleRecordCacheDuration(), $remoteModelRequest)
            : $remoteModelRequest();

        return $logs
            ->when(fn (Collection $item) => $item->keys()->contains('data'), fn (Collection $item) => collect($item->get('data')))
            ->when(
                // Handle single record responses (when response is a single object, not an array of records)
                fn (Collection $item) => $item->keys()->every(fn($key) => is_string($key)) && !$item->has(0),
                fn (Collection $item) => collect([$item->toArray()])
            )
            ->map(function ($item, $key) {
                // Handle top-level keyed responses (rarely used with Signhost API)
                if (is_string($key)) {
                    return in_array($key, $this->getJsonFields(), true) ? json_encode($item) : $item;
                }

                // Convert array values to JSON strings for Sushi storage
                // Custom casts will handle the reverse transformation during attribute access
                foreach ($item as $itemKey => $itemValue) {
                    $item[$itemKey] = is_array($itemValue) ? Utils::jsonEncode($itemValue) : $itemValue;
                }

                return $item;
            });
    }
}
