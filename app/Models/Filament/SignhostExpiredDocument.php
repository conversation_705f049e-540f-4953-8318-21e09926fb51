<?php

namespace App\Models\Filament;

use App\Models\Filament\Casts\SignhostJsonCast;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

/**
 * @property string $transaction_id
 * @property string $transaction_uuid
 * @property string $reference
 * @property int $status
 * @property string $status_text
 * @property int $sign_request_mode
 * @property bool $send_email_notifications
 * @property bool $seal
 * @property string $postback_url
 * @property int $days_to_expire
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class SignhostExpiredDocument extends Model implements RemoteModel
{
    use Sushi, SushiBuilder, RemoteModelDefaults;

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'transaction_id',
        'transaction_uuid',
        'reference',
        'language',
        'status',
        'status_text',
        'sign_request_mode',
        'send_email_notifications',
        'seal',
        'reference',
        'postback_url',
        'document',
        'days_to_expire',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'string',
            'transaction_id' => 'string',
            'transaction_uuid' => 'string',
            'reference' => 'string',
            'language' => 'string',
            'status' => 'int',
            'status_text' => 'string',
            'sign_request_mode' => 'int',
            'send_email_notifications' => 'bool',
            'seal' => 'bool',
            'postback_url' => 'string',
            'days_to_expire' => 'int',
            'context' => 'array',
            'document' => SignhostJsonCast::class,
            'transaction' => SignhostJsonCast::class,
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    public function getSchema(): array
    {
        return [
            'id' => 'string',
            'transaction_id' => 'string',
            'transaction_uuid' => 'string',
            'reference' => 'string',
            'language' => 'string',
            'status' => 'integer',
            'status_text' => 'string',
            'sign_request_mode' => 'integer',
            'send_email_notifications' => 'boolean',
            'seal' => 'boolean',
            'postback_url' => 'string',
            'days_to_expire' => 'integer',
            'context' => 'json',
            'document' => 'json',
            'transaction' => 'json',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    public function getRows(): array
    {
        return $this->getData($this->query)
            ->toArray();
    }

    /**
     * Temporarily disable Sushi caching to avoid array-to-string conversion issues
     */
    public function sushiShouldCache(): bool
    {
        return false;
    }

    public function migrate(): void
    {
        $this->createTableWithNoData($this->getTable());

        $this->getData($this->query)
            ->unless(
                fn ($data) => $data->count() > 2,
                fn ($data) => $data->map(fn ($item) => [$item])
            )
            ->filter(fn ($item) => ! array_is_list($item))
            ->each(fn ($item) => self::insert($item));
    }

    /**
     * Get fields that require JSON encoding during API data retrieval.
     *
     * Used by SushiBuilder to determine which fields need JSON encoding when
     * processing data from the Signhost API. This method is required for proper
     * data handling during API communication.
     *
     * Note: Field transformations during model instantiation are now handled
     * automatically by SignhostJsonCast for all listed fields.
     */
    protected function getJsonFields(): array
    {
        return [
            'transaction',
            'document',
        ];
    }

    public function remoteUrl(): string
    {
        return 'documents/expired';
    }
}
