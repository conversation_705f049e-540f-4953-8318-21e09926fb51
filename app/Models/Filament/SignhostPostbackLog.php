<?php

namespace App\Models\Filament;

use App\Models\Filament\Casts\SignhostJsonCast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Sushi\Sushi;

class SignhostPostbackLog extends Model implements RemoteModel
{
    use <PERSON><PERSON>, <PERSON>shiBuilder, RemoteModelDefaults;

    protected $fillable = [
        'id',
        'postback_id',
        'transaction_uuid',
        'action',
        'succeeded',
        'message',
        'postback',
    ];

    protected function casts(): array
    {
        return [
            'postback_id' => 'int',
            'transaction_uuid' => 'string',
            'action' => 'string',
            'succeeded' => 'bool',
            'message' => 'string',
            'transaction' => SignhostJsonCast::class,
            'postback' => SignhostJsonCast::class,
            'signhost_postback' => SignhostJsonCast::class,
        ];
    }

    /**
     * Temporarily disable Sushi caching to avoid array-to-string conversion issues
     */
    public function sushiShouldCache(): bool
    {
        return false;
    }

    public function getRows(): array
    {
        return $this->getData($this->query)
            ->filter(fn ($item) => ! array_is_list($item))
            ->toArray();
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(SignhostTransactionLog::class, 'transaction_uuid', 'transaction_id');
    }

    /**
     * Get fields that require JSON encoding during API data retrieval.
     *
     * Used by SushiBuilder to determine which fields need JSON encoding when
     * processing data from the Signhost API. This method is required for proper
     * data handling during API communication.
     *
     * Note: Field transformations during model instantiation are now handled
     * automatically by SignhostJsonCast for all listed fields.
     */
    protected function getJsonFields(): array
    {
        return [
            'postback',
            'signhost_postback',
            'transaction',
        ];
    }

    public function remoteUrl(): string
    {
        return 'logs/postbacks';
    }
}
