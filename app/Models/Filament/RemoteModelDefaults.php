<?php

namespace App\Models\Filament;

trait RemoteModelDefaults
{
    public function remoteSingleUrl(string $id): string
    {
        return $this->remoteUrl() . '/' . $id;
    }

    protected function getSingleRecordCacheKey(string $id): string
    {
        return sprintf('remote_model:%s:%s', static::class, $id);
    }

    protected function getRecordsCacheKey(array $query = []): string
    {
        return sprintf('remote_model:%s:%s:all', static::class, json_encode($query));
    }

    protected function getSingleRecordCacheDuration(): int
    {
        return 300; // 5 minutes
    }

    public function transformSingleRecordData(array $value): array
    {
        return $value;
    }
}
