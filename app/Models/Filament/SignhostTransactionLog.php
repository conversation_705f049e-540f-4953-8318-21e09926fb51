<?php

namespace App\Models\Filament;

use App\Models\Filament\Casts\SignhostJsonCast;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

class SignhostTransactionLog extends Model implements RemoteModel
{
    use Sushi, SushiBuilder, RemoteModelDefaults;

    protected $fillable = [
        'id',
        'transaction_id',
        'transaction_uuid',
        'action',
        'form_data',
        'is_just_eat',
        'transaction',
        'receivers',
        'signers',
        'files',
        'status',
        'details',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'int',
            'transaction_id' => 'int',
            'transaction_uuid' => 'string',
            'action' => 'string',
            'is_just_eat' => 'bool',
            'status' => 'bool',
            'form_data' => SignhostJsonCast::class,
            'transaction' => SignhostJsonCast::class,
            'receivers' => SignhostJsonCast::class,
            'signers' => SignhostJsonCast::class,
            'files' => SignhostJsonCast::class,
            'details' => SignhostJsonCast::class,
        ];
    }

    public function getRows(): array
    {
        return $this->getData($this->query)
            ->toArray();
    }

    /**
     * Temporarily disable Sushi caching to avoid array-to-string conversion issues
     */
    public function sushiShouldCache(): bool
    {
        return false;
    }

    /**
     * Get fields that require JSON encoding during API data retrieval.
     *
     * Used by SushiBuilder to determine which fields need JSON encoding when
     * processing data from the Signhost API. This method is required for proper
     * data handling during API communication.
     *
     * Note: Field transformations during model instantiation are now handled
     * automatically by SignhostJsonCast for all listed fields.
     */
    protected function getJsonFields(): array
    {
        return [
            'form_data',
            'files',
            'signers',
            'receivers',
            'transaction',
            'details',
        ];
    }

    public function remoteUrl(): string
    {
        return 'logs/transactions';
    }
}
