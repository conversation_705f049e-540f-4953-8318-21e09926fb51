<?php

namespace App\Models\Filament\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use JsonException;
use Throwable;

/**
 * Custom Eloquent cast for Signhost API request headers.
 *
 * Transforms headers from various input formats (strings, collections, JSON)
 * into consistent associative arrays for display and storage.
 *
 * Supported input formats:
 * - JSON-encoded arrays (from database storage)
 * - Comma-separated header strings: "key1: value1, key2: value2"
 * - Bracketed header strings: "[key1: value1, key2: value2]"
 * - Collections/arrays of header strings
 * - Already processed associative arrays
 *
 * @package App\Models\Filament\Casts
 */
class SignhostHeaderCast implements CastsAttributes
{
    /**
     * Cast the given value to an associative array.
     *
     * Transforms headers from various input formats into a consistent associative array.
     * Handles JSON-encoded data from storage, raw header strings from APIs, and collections.
     *
     * @param Model $model The Eloquent model instance
     * @param string $key The attribute key being cast
     * @param mixed $value The raw value to be cast
     * @param array<string, mixed> $attributes All model attributes
     * @return array<string, string>|null The processed headers as associative array or null
     *
     * @example
     * Input: "[Accept: application/json, Authorization: Bearer token]"
     * Output: ["Accept" => " application/json", "Authorization" => " Bearer token"]
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): array|null
    {
        // Early return for null values
        if ($value === null) {
            return null;
        }

        // Early return for already processed arrays
        if (is_array($value)) {
            return $value;
        }

        // Handle string values (JSON or raw header strings)
        if (is_string($value)) {
            // Early return for empty strings
            if (trim($value) === '') {
                return [];
            }

            // Try to decode as JSON first (for stored data)
            try {
                $decoded = json_decode($value, true, 512, JSON_THROW_ON_ERROR);
                if (is_array($decoded)) {
                    // Check if it's already a processed associative array (key-value pairs)
                    if ($this->isAssociativeArray($decoded)) {
                        return $decoded; // Already processed, return as-is
                    }

                    // Handle JSON array of header strings (common format from Signhost API)
                    return collect($decoded)
                        ->filter(fn($item) => is_string($item) && trim($item) !== '')
                        ->map(fn(string $headerString) => $this->formatHeader($headerString))
                        ->collapse() // Flatten all header arrays into a single associative array
                        ->toArray();
                }
            } catch (JsonException) {
                // Not JSON, continue to header string processing
            }

            // Process as a single header string
            return $this->formatHeader($value);
        }

        // Handle collections/iterables of header strings
        if (is_iterable($value)) {
            try {
                return collect($value)
                    ->filter(fn($item) => is_string($item) && trim($item) !== '')
                    ->map(fn(string $headerString) => $this->formatHeader($headerString))
                    ->collapse() // Flatten all header arrays into a single associative array
                    ->toArray();
            } catch (Throwable $e) {
                // Log error and return empty array for robustness
                Log::warning('SignhostHeaderCast: Failed to process iterable headers', [
                    'value' => $value,
                    'error' => $e->getMessage()
                ]);
                return [];
            }
        }

        // Fallback: return empty array for unexpected types
        Log::warning('SignhostHeaderCast: Unexpected value type', [
            'type' => gettype($value),
            'value' => $value
        ]);

        return [];
    }

    /**
     * Prepare the given value for storage in the database.
     *
     * Converts the processed headers array into JSON format for database storage.
     * Handles various input types and ensures consistent JSON encoding.
     *
     * @param Model $model The Eloquent model instance
     * @param string $key The attribute key being cast
     * @param mixed $value The value to be stored
     * @param array<string, mixed> $attributes All model attributes
     * @return string|null JSON-encoded headers or null
     *
     * @throws JsonException When JSON encoding fails
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): string|null
    {
        // Early return for null values
        if ($value === null) {
            return null;
        }

        try {
            // If value is already an array, encode directly
            if (is_array($value)) {
                return json_encode($value, JSON_THROW_ON_ERROR);
            }

            // For other types, process through get() method first, then encode
            $processed = $this->get($model, $key, $value, $attributes);
            return json_encode($processed, JSON_THROW_ON_ERROR);

        } catch (JsonException $e) {
            Log::error('SignhostHeaderCast: JSON encoding failed', [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);

            // Return empty JSON array as fallback
            return '[]';
        }
    }

    /**
     * Format a single header string into an associative array.
     *
     * Parses comma-separated "key: value" pairs with robust error handling.
     * Handles malformed headers gracefully by skipping invalid pairs.
     *
     * @param string $header The header string to format (e.g., "Accept: application/json, Auth: Bearer token")
     * @return array<string, string> The formatted associative array
     *
     * @example
     * Input: "Accept: application/json, Authorization: Bearer token"
     * Output: ["Accept" => " application/json", "Authorization" => " Bearer token"]
     */
    private function formatHeader(string $header): array
    {
        // Early return for empty strings
        if (trim($header) === '') {
            return [];
        }

        try {
            // Split the header string by commas to get individual key-value pairs
            $headerPairs = explode(',', $header);

            // Remove brackets and clean up each pair
            $cleanedPairs = array_map(function (string $pair): string {
                return Str::of($pair)
                    ->replace(['[', ']'], '') // Remove brackets
                    ->trim() // Remove leading/trailing whitespace
                    ->toString();
            }, $headerPairs);

            // Build associative array from key-value pairs
            $headerArray = [];
            foreach ($cleanedPairs as $pair) {
                // Skip empty pairs
                if ($pair === '') {
                    continue;
                }

                // Find the first colon to separate key and value
                // This handles cases with multiple colons in the value (e.g., URLs)
                $colonPosition = strpos($pair, ':');
                if ($colonPosition !== false && $colonPosition > 0) {
                    $headerKey = trim(substr($pair, 0, $colonPosition));
                    $headerValue = substr($pair, $colonPosition + 1);

                    // Only add non-empty keys
                    if ($headerKey !== '') {
                        $headerArray[$headerKey] = $headerValue;
                    }
                }
            }

            return $headerArray;

        } catch (Throwable $e) {
            Log::warning('SignhostHeaderCast: Failed to format header string', [
                'header' => $header,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Check if an array is associative (has string keys) rather than indexed.
     *
     * @param array $array The array to check
     * @return bool True if the array is associative, false if indexed
     */
    private function isAssociativeArray(array $array): bool
    {
        if (empty($array)) {
            return false;
        }

        // Check if all keys are strings (associative) vs integers (indexed)
        return !array_is_list($array);
    }
}
