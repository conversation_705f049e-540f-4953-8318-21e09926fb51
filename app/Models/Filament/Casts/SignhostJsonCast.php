<?php

namespace App\Models\Filament\Casts;

use GuzzleHttp\Utils;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use JsonException;
use Throwable;

/**
 * Generic Eloquent cast for Signhost JSON fields.
 *
 * Transforms JSON strings from APIs into arrays for display and storage.
 * Handles various input formats with robust error handling and logging.
 *
 * Supported input formats:
 * - JSON strings from API responses
 * - Already decoded arrays (from storage or previous processing)
 * - Null values
 *
 * This cast uses GuzzleHttp\Utils::jsonDecode for consistency with existing
 * Signhost model transformations and provides the same error handling.
 *
 * @package App\Models\Filament\Casts
 */
class SignhostJsonCast implements CastsAttributes
{
    /**
     * Cast the given value to an array.
     *
     * Transforms JSON strings into arrays using the same logic as the existing
     * transformSingleRecordData methods across Signhost models.
     *
     * @param Model $model The Eloquent model instance
     * @param string $key The attribute key being cast
     * @param mixed $value The raw value to be cast
     * @param array<string, mixed> $attributes All model attributes
     * @return array|null The processed JSON as array or null
     *
     * @example
     * Input: '{"key": "value", "nested": {"data": "example"}}'
     * Output: ["key" => "value", "nested" => ["data" => "example"]]
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): array|null
    {
        // Early return for null values
        if ($value === null) {
            return null;
        }

        // Early return for already processed arrays
        if (is_array($value)) {
            return $value;
        }

        // Handle string values (JSON from API or storage)
        if (is_string($value)) {
            // Early return for empty strings
            if (trim($value) === '') {
                return [];
            }

            try {
                // Use GuzzleHttp\Utils::jsonDecode for consistency with existing transformations
                return Utils::jsonDecode($value, assoc: true);

            } catch (Throwable $e) {
                Log::warning('SignhostJsonCast: Failed to decode JSON', [
                    'model' => get_class($model),
                    'key' => $key,
                    'value' => $value,
                    'error' => $e->getMessage()
                ]);

                // Return empty array as fallback for robustness
                return [];
            }
        }

        // Fallback: return empty array for unexpected types
        Log::warning('SignhostJsonCast: Unexpected value type', [
            'model' => get_class($model),
            'key' => $key,
            'type' => gettype($value),
            'value' => $value
        ]);

        return [];
    }

    /**
     * Prepare the given value for storage in the database.
     *
     * Converts arrays into JSON format for database storage.
     * Handles various input types and ensures consistent JSON encoding.
     *
     * @param Model $model The Eloquent model instance
     * @param string $key The attribute key being cast
     * @param mixed $value The value to be stored
     * @param array<string, mixed> $attributes All model attributes
     * @return string|null JSON-encoded data or null
     *
     * @throws JsonException When JSON encoding fails
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): string|null
    {
        // Early return for null values
        if ($value === null) {
            return null;
        }

        try {
            // If value is already an array, encode directly
            if (is_array($value)) {
                return json_encode($value, JSON_THROW_ON_ERROR);
            }

            // For other types, process through get() method first, then encode
            $processed = $this->get($model, $key, $value, $attributes);
            return json_encode($processed, JSON_THROW_ON_ERROR);

        } catch (JsonException $e) {
            Log::error('SignhostJsonCast: JSON encoding failed', [
                'model' => get_class($model),
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);

            // Return empty JSON array as fallback
            return '[]';
        }
    }
}
