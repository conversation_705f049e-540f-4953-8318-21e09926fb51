<?php

namespace App\Providers\Filament;

use App\Filament\Resources\ImportResource\Pages\ListImports;
use App\Filament\Resources\JetApplicantImportResource\Pages\ListJetApplicantImports;
use App\Filament\Resources\JetApplicantResource\Pages\ListApprovedJetApplicants;
use App\Filament\Resources\JetApplicantResource\Pages\ListGhostRiders;
use App\Filament\Resources\JetApplicantResource\Pages\ListJetApplicants;
use App\Filament\Resources\JetApplicantResource\Pages\ListNewJetApplicants;
use App\Filament\Resources\JetApplicantResource\Pages\ListOnHoldJetApplicants;
use App\Filament\Resources\JetApplicantResource\Pages\ListRejectedJetApplicants;
use App\Filament\Resources\JetApplicantResource\Pages\ListTestJetApplicants;
use App\Filament\Resources\JetApplicantNewResource\Pages as JetApplicantResourcePages;
use App\Filament\Resources\ResidencePermitResource;
use App\Filament\Resources\StickerResource;
use App\Filament\Resources\TwvResource;
use App\Http\Middleware\SentryContext;
use App\Filament\Resources\JetCourierDocumentRequestLogResource;
use Filament\FontProviders\LocalFontProvider;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Support\Enums\MaxWidth;

class AdminPanelProvider extends PanelProvider
{

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->maxContentWidth(MaxWidth::Full)
            ->login()
            ->darkMode(false)
            ->topbar(false)
            ->collapsibleNavigationGroups(false)
            ->viteTheme('resources/css/filament/admin/theme.scss')
            ->font(
                'ClearSans',
                url: asset('css/fonts.css'),
                provider: LocalFontProvider::class,
            )
            ->brandLogo(fn () => view('filament.admin.logo'))
            ->colors([
                'danger' => [
                    100 => '#f1d3d8',
                    200 => '#e3a7b1',
                    300 => '#d47c8a',
                    400 => '#c65063',
                    500 => '#b8243c',
                    600 => '#931d30',
                    700 => '#6e1624',
                    800 => '#4a0e18',
                    900 => '#25070c'
                ],

                'info' => [
                    100 => '#d5e3f5',
                    200 => '#aac7eb',
                    300 => '#80abe0',
                    400 => '#558fd6',
                    500 => '#2b73cc',
                    600 => '#225ca3',
                    700 => '#1a457a',
                    800 => '#112e52',
                    900 => '#091729'
                ],
                'primary' => [
                    100 => '#F7F9FC',
                    200 => '#E9EFF7',
                    300 => '#71859d',
                    400 => '#4C5A6B',
                    500 => '#13335b',
                    600 => '#0E2542',
                    700 => '#0b1f37',
                    800 => '#081424',
                    900 => '#040a12'
                ],
                'success' => [
                    100 => '#cde1d8',
                    200 => '#9bc2b1',
                    300 => '#69a48a',
                    400 => '#378563',
                    500 => '#05673c',
                    600 => '#045230',
                    700 => '#033e24',
                    800 => '#022918',
                    900 => '#01150c'
                ],
            ])
            ->navigationGroups([
                NavigationGroup::make()
                    ->label('Controle')
                    ->icon('heroicon-o-check-badge'),

                NavigationGroup::make()
                    ->label('Imports')
                    ->icon('heroicon-o-arrow-left-end-on-rectangle'),

                NavigationGroup::make()
                    ->label('Permits')
                    ->icon('heroicon-o-inbox-stack'),

                NavigationGroup::make()
                    ->label('Jet applicants')
                    ->icon('heroicon-o-users'),

                NavigationGroup::make()
                    ->label('Jet applicants (New)')
                    ->icon('heroicon-o-users'),

                NavigationGroup::make()
                    ->label('Loon info')
                    ->icon('heroicon-o-currency-euro'),

                NavigationGroup::make()
                    ->label('Filament Shield')
                    ->icon('heroicon-o-shield-check'),
            ])
            ->navigationItems([
                NavigationItem::make('Loon componenten')
                    ->url(fn (): string => ListImports::getUrl())
                    ->group('Imports')
                    ->isActiveWhen(fn () => request()->fullUrlIs(ListImports::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_import'))
                ,
                NavigationItem::make('Verblijfsvergunningen process')
                    ->url(fn (): string => JetCourierDocumentRequestLogResource::getUrl())
                    ->group('Controle')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetCourierDocumentRequestLogResource::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant')),

                NavigationItem::make('Jet applicants contractverlengingen')
                    ->url(fn (): string => ListJetApplicantImports::getUrl())
                    ->group('Imports')
                    ->isActiveWhen(fn () => request()->fullUrlIs(ListJetApplicantImports::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_import'))
                ,
                // NavigationItem::make('All applicants')
                //     ->url(fn (): string => ListJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                // ,
                // NavigationItem::make('New applicants')
                //     ->url(fn (): string => ListNewJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListNewJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                // ,
                // NavigationItem::make('Approved applicants')
                //     ->url(fn (): string => ListApprovedJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListApprovedJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                // ,
                // NavigationItem::make('Rejected applicants')
                //     ->url(fn (): string => ListRejectedJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListRejectedJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                // ,
                // NavigationItem::make('On hold applicants')
                //     ->url(fn (): string => ListOnHoldJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListOnHoldJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                // ,
                // NavigationItem::make('Test applicants')
                //     ->url(fn (): string => ListTestJetApplicants::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListTestJetApplicants::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->hasRole('super_admin'))
                // ,
                // NavigationItem::make('Ghost riders')
                //     ->url(fn (): string => ListGhostRiders::getUrl())
                //     ->group('Jet applicants')
                //     ->isActiveWhen(fn () => request()->fullUrlIs(ListGhostRiders::getUrl()))
                //     ->visible(fn(): bool => auth()->user()->hasRole('super_admin'))
                // ,
                NavigationItem::make('Verblijfsvergunningen')
                    ->url(fn (): string => ResidencePermitResource::getUrl())
                    ->group('Permits')
                    ->isActiveWhen(fn () => request()->fullUrlIs(ResidencePermitResource::getUrl()))
                ,
                NavigationItem::make("TWV's")
                    ->url(fn (): string => TwvResource::getUrl())
                    ->group('Permits')
                    ->isActiveWhen(fn () => request()->fullUrlIs(TwvResource::getUrl()))
                ,

                NavigationItem::make("Sticker's")
                    ->url(fn (): string => StickerResource::getUrl())
                    ->group('Permits')
                    ->isActiveWhen(fn () => request()->fullUrlIs(StickerResource::getUrl()))
                ,

                // New:
                NavigationItem::make('All applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                ,
                NavigationItem::make('New applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListNewJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListNewJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                ,
                NavigationItem::make('Approved applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListApprovedJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListApprovedJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                ,
                NavigationItem::make('Rejected applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListRejectedJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListRejectedJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                ,
                NavigationItem::make('On hold applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListOnHoldJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListOnHoldJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->can('view_jet::applicant'))
                ,
                NavigationItem::make('Test applicants')
                    ->url(fn (): string => JetApplicantResourcePages\ListTestJetApplicants::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListTestJetApplicants::getUrl()))
                    ->visible(fn(): bool => auth()->user()->hasRole('super_admin'))
                ,
                NavigationItem::make('Ghost riders')
                    ->url(fn (): string => JetApplicantResourcePages\ListGhostRiders::getUrl())
                    ->group('Jet applicants (New)')
                    ->isActiveWhen(fn () => request()->fullUrlIs(JetApplicantResourcePages\ListGhostRiders::getUrl()))
                    ->visible(fn(): bool => auth()->user()->hasRole('super_admin'))
                ,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
            ])
            ->plugins([
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make(),

            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SentryContext::class,
            ])
            ->resources([
                config('filament-logger.activity_resource')
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
