<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Filament\RemoteModel;
use Closure;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class SignhostService
{
    private PendingRequest $signhostClient;

    private $statuses = [
        5 => 'Waiting for document',
        10 => 'Waiting for signer',
        20 => 'In progress',
        30 => 'Signed',
        40 => 'Rejected',
        50 => 'Expired',
        60 => 'Cancelled',
        70 => 'Failed'
    ];

    public function __construct()
    {
        $this->signhostClient = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])
            ->baseUrl(config('services.signhost_service.url'))
            ->throw();
    }

    public function getContracts($flexapp_id)
    {
            $response = $this->signhostClient->get(config('services.signhost_service.url') . '/get_local_transaction/' . $flexapp_id)->json();

        return $response['data'] ?? [];
    }

    public function cancelAllContracts($flexapp_id)
    {
        $contracts = $this->getContracts($flexapp_id);
        foreach($contracts as $contract) {
            if ($contract['attributes']['status_code'] > 30 && ! str_starts_with(data_get($contract, 'attributes.reference'), 'JET')) {
                continue;
            }
            $this->cancelContract($contract['attributes']['transaction_id']);
        }
    }

    public function cancelContract($transactionId): void
    {
        Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])->delete(config('services.signhost_service.url') . '/delete_transaction/' . $transactionId);
    }

    public function modelRequest(RemoteModel $remoteModel, array $query = [], ?array $fields = []): Collection
    {
        $url = array_key_exists('id', $query)
            ? $remoteModel->remoteSingleUrl(data_get($query, 'id'))
            : $remoteModel->remoteUrl();

        $callback = fn (string $url, array $query, ?array $fields): Closure => function () use ($url, $query, $fields) {
            try {
                return $this->signhostClient
                    ->get($url, $query)
                    ->collect($fields);
            } catch (ConnectionException|RequestException $e) {
                report($e);

                return collect();
            }
        };

        $key = json_encode(compact('url', 'query', 'fields'));

        return $remoteModel->sushiShouldCache()
            ? Cache::remember($key, now()->addMinutes(5), $callback($url, $query, $fields))
            : $callback($url, $query, $fields)();
    }
}
