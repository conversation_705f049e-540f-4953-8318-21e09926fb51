<?php

namespace App\Services;

use App\Livewire\DataCheckerDocuments;
use App\Models\ResidencePermitRenewal;
use App\Models\TwvInformation;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use SoapClient;
use SoapFault;

class EasyflexService
{

    /* JET WKM_id is 7 */
    private int $wkmId = 7;

    public function storeEasyflexUser($record)
    {
        if (! $record->wenote_id) {
            if (! isset($record->wenoteData->UKT_id) || $record->wenoteData->UKT_id == null) {
                return Notification::make()
                    ->title('Gebruiker heeft geen Wenote ID')
                    ->danger()
                    ->send();
            }
            $record->wenote_id = $record->wenoteData->UKT_id;
            $record->save();
        }

        if (! $record->easyflex_id && $record->wenote_id) {
            $data = [
                'ukt_id' => $record->wenote_id,
                'start_date' => $record->flexappData->info->start_date,
            ];

            $result = Http::asForm()->post(config('services.wenote.app_url') . '/api/sync_jet.php', $data);

            if (stripos($result->body(), 'error') !== false) {
                return Notification::make()
                    ->title('Gebruiker heeft geen Easyflex ID. <br>' . $result->body())
                    ->persistent()
                    ->danger()
                    ->send();
            }

            $record->easyflex_id = $result->body();
            $record->save();

            Notification::make()
                ->title('Gebruiker naar Easyflex verstuurd.')
                ->persistent()
                ->success()
                ->send();

            if (! $this->storeLoonInfo($record)) {
                Notification::make()
                    ->title('Gebruiker aangemaakt, maar versturen van looninformatie naar Easyflex mislukt.')
                    ->persistent()
                    ->danger()
                    ->send();
            }

            if (isset($record->flexappData->drivingLicense->document_number)) {
                $response = $this->storeDriverLicense($record);
                $failed = ! $response instanceof \Illuminate\Http\JsonResponse ||
                    $response->getStatusCode() !== 200 ||
                    ! data_get($response->getData(), 'success');
                if ($failed) {
                    $message = data_get($response->getData(), 'message', 'Versturen van rijbewijs mislukt.');
                    Notification::make()
                        ->title('Rijbewijs niet verstuurd')
                        ->body($message)
                        ->persistent()
                        ->danger()
                        ->send();
                }
            }
            if (isset($record->flexappData->identification->document_type) && $record->flexappData->identification->document_type === 'Verblijfsvergunning') {
                $response = $this->storeResidencePermit($record);
                $failed = ! $response instanceof \Illuminate\Http\JsonResponse ||
                    $response->getStatusCode() !== 200 ||
                    ! data_get($response->getData(), 'success');
                if ($failed) {
                    $message = data_get($response->getData(), 'message', 'Versturen van verblijfsvergunning mislukt.');
                    Notification::make()
                        ->title('Verblijfsvergunning niet verstuurd')
                        ->body($message)
                        ->persistent()
                        ->danger()
                        ->send();
                }
            }
            return true;
        }

        return Notification::make()
            ->title('Er ging wat fout bij het sturen')
            ->persistent()
            ->danger()
            ->send();
    }

    public function updatePlaatsingEinde($params)
    {
        $token = $this->getToken();
        $response = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json'
        ])->withToken($token)
            ->patch(
                config('services.easyflex_service.url') . '/update_plaatsing_einde/',
                $params
            );

        if ($response->failed()) {
            Log::error('easyflex update_plaatsing_einde call failed', [
                'responseData' => $response->json(),
                'params' => $params,
                'httpCode' => $response->status(),
            ]);

            return false;
        }

        return $response->json();
    }

    public function insertIdDocument($params)
    {
        $token = $this->getToken();
        $response = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json'
        ])->withToken($token)
            ->post(
                config('services.easyflex_service.url') . '/insert_id_document/',
                $params
            );

        if ($response->failed()) {
            Log::error('easyflex insert_id_document call failed', [
                'responseData' => $response->json(),
                'params' => Arr::except($params, 'file'),
                'httpCode' => $response->status(),
            ]);

            return false;
        }

        return $response->json();
    }

    public function updateEasyflexUser($params)
    {
        $token = $this->getToken();

        $response = Http::withToken($token)
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])->patch(
                config('services.easyflex_service.url') . '/update_persoonsgegevens',
                $params
            );

        if ($response->failed()) {
            Log::error('easyflex update_persoonsgegevens call failed', [
                'responseData' => $response->json(),
                'params' => $params,
                'httpCode' => $response->status(),
            ]);

            return false;
        }

        return true;
    }

    public function getPersoonsgegevens($easyflexId)
    {
        $response = Http::baseUrl(config('services.easyflex_service.url'))
            ->withToken($this->getToken())
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])->post('/get_persoonsgegevens', [
                'registratienummer' => $easyflexId,
                'WKM_id' => $this->wkmId
            ]);

        if($response->failed()){
            Log::error('easyflex get_persoonsgegevens call failed', [
                'responseData' => $response->json(),
                'easyflexId' => $easyflexId,
                'httpCode' => $response->status(),
            ]);

            return false;
        }

        return json_decode($response->body())->data;
    }

    public function offboardUserEasyflex($details)
    {
        $response = Http::baseUrl(config('services.easyflex_service.url'))
            ->withToken($this->getToken())
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])->post('/offboard_user', [
                ...$details,
                'WKM_id' => $this->wkmId,
            ]);

        if ($response->failed()) {
            Notification::make()
                ->title('Failed updating easyflex.')
                ->persistent()
                ->danger()
                ->send();

            Log::error('easyflex offboard_user call failed', [
                'responseData' => $response->json(),
                'details' => $details,
                'httpCode' => $response->status(),
            ]);
        }

        Notification::make()
            ->title('Updated in easyflex')
            ->persistent()
            ->success()
            ->send();
    }

    public function storeLoonInfo($record)
    {
        $registratienummer = trim($record->easyflex_id);
        $ingangsdatum = $record->flexappData->info->start_date;
        $with_loonheffingskorting = $record->flexappData->financialDetails->loonheffing;

        $token = $this->getToken();
        $salaryParams = [
            'registratienummer' => $registratienummer,
            'WKM_id' => $this->wkmId,
            'ingangsdatum' => $ingangsdatum,
        ];

        $postLoonopgave = Http::baseUrl(config('services.easyflex_service.url'))
            ->withToken($token)
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])
            ->post('/insert_loonopgave', [
                ...$salaryParams,
                'with_loonheffingskorting' => $with_loonheffingskorting,
            ]);

        $postLoontijdvak = Http::baseUrl(config('services.easyflex_service.url'))
            ->withToken($token)
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])
            ->post('/insert_loontijdvak', [
                ...$salaryParams,
            ]);

        if ($postLoonopgave->failed()) {
            Notification::make()
                ->title('Failed inserting loonopgave')
                ->body(sprintf(
                    'Registratie nr. %s, WKM_id: %s, start date: %s, loonheffing: %s',
                    $registratienummer,
                    $this->wkmId,
                    $ingangsdatum,
                    $with_loonheffingskorting ? 'Yes' : 'No',
                ))
                ->persistent()
                ->danger()
                ->send();

            Log::error('Failed inserting loonopgave', [
                ...$salaryParams,
                'with_loonheffingskorting' => $with_loonheffingskorting,
            ]);
        }

        if ($postLoontijdvak->failed()) {
            Notification::make()
                ->title('Failed inserting loontijdvak.')
                ->body(sprintf(
                    'Registratie nr. %s, WKM_id: %s, start date: %s, loonheffing: %s',
                    $registratienummer,
                    $this->wkmId,
                    $ingangsdatum,
                    $with_loonheffingskorting ? 'Yes' : 'No',
                ))
                ->persistent()
                ->danger()
                ->send();

            Log::error('Failed inserting loontijdvak', $salaryParams);
        }

        return $postLoonopgave->successful() && $postLoontijdvak->successful();
    }

    public function updateHours($params)
    {

    }

    public function storeTwvFile($record, $data)
    {
        $twvInformation = TwvInformation::where('applicant_id', $record->applicant_id)
            ->orderby('created_at', 'desc')
            ->firstOrNew(['applicant_id' => $record->applicant_id]);

        $twvInformation->twv_approved_date = $data['twv_approved_date'] ?? $twvInformation->twv_approved_date;
        $twvInformation->twv_issued_by = $data['twv_issued_by'] ?? $twvInformation->twv_issued_by;
        $twvInformation->twv_number = $data['twv_number'] ?? $twvInformation->twv_number;
        $twvInformation->twv_start_date = $data['twv_start_date'] ?? $twvInformation->twv_start_date;
        $twvInformation->twv_expiration_date = $data['twv_expiration_date'] ?? $twvInformation->twv_expiration_date;
        $twvInformation->twv_file_name = $data['twv_file'] ?? $twvInformation->twv_file_name;
        $twvInformation->save();

        if(!empty($data['twv_approved_date'])){
            // send data to userservice
            $record->flexappData->info->right_to_work = true;
            $record->flexappData->info->twv_approved = true;
            $record->flexappData->info->twv_number = $data['twv_number'];
            $record->flexappData->info->twv_approved_date = $data['twv_approved_date'];
            $record->flexappData->info->twv_start_date = $data['twv_start_date'];
            $record->flexappData->info->twv_expiration_date = $data['twv_expiration_date'];
            $record->flexappData->info->save();
        }

        if(!empty($data['twv_approved_date']) && isset($data['twv_file'])) {
            // send file & data to easyflex
            $file_path = $data['twv_file'];
            $file_name = basename($file_path);
            $disk_file = Storage::disk('identification')->get($file_path);
            $file = base64_encode($disk_file);

            $params = [
                "registratienummer" => $record->easyflex_id,
                "WKM_id" => 7,
                "documentsoort" => 17202,
                "documentomschrijving" => 'TWV bestand '.Carbon::now()->format('d-m-Y'),
                "file_name" => $file_name,
                "file" => $file,
                "tewerkafgegevendoor" => $data['twv_issued_by'],
                "tewerkafgegevenop" => $data['twv_start_date'],
                "tewerknummer" => $data['twv_number'],
                "tewerkgeldigtot" => $data['twv_expiration_date']
            ];

            $documentUploaded = $this->insertIdDocument($params);

            if (isset($documentUploaded['data']['ds_fw_id_document_insert_result']) && $documentUploaded['data']['ds_fw_id_document_insert_result'] == true) {
                $twvInformation->twv_file_name = $file_path;
                $twvInformation->easyflex_document_number = $documentUploaded['data']['ds_fw_id_document_insert_regnr'];
                $twvInformation->save();

                // email twv to user & notify jet
                $communicationData = [
                    ...$record->only(['first_name', 'last_name', 'email', 'preferred_language', 'scoober_id']),
                    'file' => $file,
                    'file_name' => $file_name,
                    'reason' => $data['reason'] ?? null,
                    'permit_type' => 'TWV',
                    'document_expire_date' => $data['twv_expiration_date'],
                    'document_renew_date' => $data['twv_start_date'],
                ];

                $communicationService = new CommunicationService();
                $communicationService->sendTwvToUser($communicationData);

                return Notification::make()
                    ->title('Document saved in Easyflex, document number: ' . $documentUploaded['data']['ds_fw_id_document_insert_regnr'])
                    ->success()
                    ->send();
            } else {
                return Notification::make()
                    ->title('Error: document not uploaded in Easyflex')
                    ->danger()
                    ->persistent()
                    ->send();
            }
        }
    }

    public function storeDriverLicense($record)
    {
        $datachecker = new DataCheckerDocuments();
        $images = $datachecker->getImages($record->flexapp_id);
        $driverLicenseImages = isset($images['DRIVING_LICENSE']) ? $images['DRIVING_LICENSE'] : null;

        if (!isset($driverLicenseImages['FRONT'], $driverLicenseImages['BACK'])) {
            return response()->json([
                'success' => false,
                'message' => 'Afbeeldingen ontbreken',
            ], 422);
        }

        $datachecker = new DataCheckerService();
        $mostRecentDocuments = $datachecker->getMostRecentDocuments($record->flexapp_id);
        $driverLicenseInfo = $mostRecentDocuments['driverLicense']['info'];
        $driverLicenseData = $driverLicenseInfo['classes'] ?? [];

        $imageBack = $driverLicenseImages['BACK']['attributes']['preview_url'];
        $backImageData = file_get_contents($imageBack);
        if ($backImageData === false) {
            return response()->json([
                'success' => false,
                'message' => 'Kon afbeelding niet ophalen',
            ], 422);
        }
        $backBase64 = base64_encode($backImageData);
        $backFilename = $driverLicenseImages['BACK']['attributes']['file_name'];

        $licenseMapping = [
            'A1' => 'rijbewijsa1',
            'A2' => 'rijbewijsa2',
            'A' => 'rijbewijsa2',
            'B' => 'rijbewijsb',
            'C' => 'rijbewijsc',
            'D' => 'rijbewijsd',
            'BE' => 'rijbewijseb',
            'CE' => 'rijbewijsec',
            'DE' => 'rijbewijsed',
            'AM' => 'rijbewijsam',
        ];

        $mappedLicenses = [];

        foreach ($driverLicenseData as $license) {
            $key = $licenseMapping[$license['name']] ?? null;
            if ($key) {
                $mappedLicenses[$key] = 1;
            }
        }

        $driverLicenseExpirationDate = $driverLicenseInfo['date_of_expiration'];
        $driverLicenseNumber = $driverLicenseInfo['document_number'];

        $params = [
            "registratienummer" => $record->easyflex_id,
            "WKM_id" => 7,
            "documentsoort" => 17203,
            "rijbewijsnummer" => $driverLicenseNumber,
            "rijbewijsgeldigtot" => $driverLicenseExpirationDate,
            "documentomschrijving" => 'Rijbewijs achterkant ' . Carbon::now()->format('d-m-Y'),
            "file_name" => $backFilename,
            "file" => $backBase64,
            ...$mappedLicenses,
        ];

        $documentUploaded = $this->insertIdDocument($params);
        if (($documentUploaded['data']['ds_fw_id_document_insert_result'] ?? false)) {
            return response()->json([
                'success' => true,
                'message' => 'Rijbewijs succesvol opgeslagen',
            ]);
        }

        Log::error('uploaden rijbewijs mislukt', [
            'params' => Arr::except($params, 'file'),
            'uploaded document' => $documentUploaded,
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Afbeelding uploaden mislukt',
        ], 500);
    }

    private function getResidencePermitInfo($flexapp_id): ?array
    {
        $datachecker = new DataCheckerService();
        $mostRecentDocuments = $datachecker->getMostRecentDocuments($flexapp_id);
        $residencePermitInfo = data_get($mostRecentDocuments, 'residencePermit.info');
        if ($residencePermitInfo) {
            return $residencePermitInfo;
        }

        $identityInfo = data_get($mostRecentDocuments, 'identity.info');
        if (data_get($identityInfo, 'document_type') === 'RESIDENCE_PERMIT') {
            return $identityInfo;
        }

        return null;
    }

    public function storeResidencePermit($record)
    {
        $residencePermitInfo = $this->getResidencePermitInfo($record->flexapp_id);
        if (!$residencePermitInfo) {
            return response()->json([
                'success' => false,
                'message' => 'Data ontbreekt of geen verblijfsvergunning beschikbaar',
            ], 422);
        }

        //region Get images
        $datachecker = new DataCheckerDocuments();
        $images = $datachecker->getImages($record->flexapp_id);
        $residencePermitImages = isset($images['RESIDENCE_PERMIT']) ? $images['RESIDENCE_PERMIT'] : null;

        if (!isset($residencePermitImages['FRONT'], $residencePermitImages['BACK'])) {
            return response()->json([
                'success' => false,
                'message' => 'Afbeeldingen ontbreken',
            ], 422);
        }

        $imageBack = $residencePermitImages['BACK']['attributes']['preview_url'];
        $backImageData = file_get_contents($imageBack);
        if ($backImageData === false) {
            return response()->json([
                'success' => false,
                'message' => 'Kon achterkant niet ophalen',
            ], 422);
        }
        $backBase64 = base64_encode($backImageData);
        $backFilename = $residencePermitImages['BACK']['attributes']['file_name'];
        $imageFront = $residencePermitImages['FRONT']['attributes']['preview_url'];
        $frontImageData = file_get_contents($imageFront);
        if ($frontImageData === false) {
            return response()->json([
                'success' => false,
                'message' => 'Kon voorkant niet ophalen',
            ], 422);
        }
        $frontBase64 = base64_encode($frontImageData);
        $frontFilename = $residencePermitImages['BACK']['attributes']['file_name'];
        //endregion

        //region Set Easyflex parameters
        $idbewijs = data_get($residencePermitInfo, 'residence_permit_type') === 'W'
            ? 21588 // 21588: W-document asielzoekers
            : 21584; // 21584: Verblijfsdocument

        $baseParams = [
            'registratienummer' => $record->easyflex_id,
            'WKM_id' => 7,
            'documentsoort' => 17201, // 17201: Verblijfsvergunning
            'idbewijs' => $idbewijs,
            'verblijfafgegevendoor' => data_get($residencePermitInfo, 'issuing_state_or_organization'),
            'verblijfafgegevenop' => data_get($residencePermitInfo, 'date_of_issuance'),
            'verblijfnummer' => data_get($residencePermitInfo, 'document_number'),
            'verblijfgeldigtot' => data_get($residencePermitInfo, 'date_of_expiry')
        ];

        $frontParams = [
            ...$baseParams,
            'documentomschrijving' => 'Verblijfsvergunning voorkant ' . Carbon::now()->format('d-m-Y'),
            'file_name' => $frontFilename,
            'file' => $frontBase64,
        ];

        $backParams = [
            ...$baseParams,
            'documentomschrijving' => 'Verblijfsvergunning achterkant ' . Carbon::now()->format('d-m-Y'),
            'file_name' => $backFilename,
            'file' => $backBase64,
        ];

        $persoonsgegevensParams = [
            'registratienummer' => $record->easyflex_id,
            'WKM_id' => 7,
            'params' => [
                'idbewijs' => $idbewijs,
                'idbewijsnummer' => data_get($residencePermitInfo, 'document_number'),
                'idbewijsgeldigtot' => Carbon::parse(data_get($residencePermitInfo, 'date_of_expiry'))->format('Y-m-d'),
            ],
        ];
        //endregion

        $frontDocumentUploaded = $this->insertIdDocument($frontParams);
        if (!$frontDocumentUploaded) {
            return response()->json([
                'success' => false,
                'message' => 'Voorkant uploaden mislukt',
            ], 500);
        }

        $backDocumentUploaded = $this->insertIdDocument($backParams);
        if (!$backDocumentUploaded) {
            return response()->json([
                'success' => false,
                'message' => 'Achterkant uploaden mislukt',
            ], 500);
        }

        $persoonsgegevensUpdated = $this->updateEasyflexUser($persoonsgegevensParams);
        if (!$persoonsgegevensUpdated) {
            return response()->json([
                'success' => false,
                'message' => 'Verblijfsvergunning opgeslagen, maar updaten van soort ID bewijs mislukt.</br><b>Let op:</b> Pas in Easyflex zelf nog het documenttype aan.',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Verblijfsvergunning succesvol opgeslagen',
        ]);
    }

    public function getContracts($record)
    {
        $registratienummer = $record->easyflex_id;
        if (is_null($registratienummer)) {
            return null;
        }

        $response = Http::baseUrl(config('services.easyflex_service.url'))
            ->withToken($this->getToken())
            ->withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])
            ->post('/get_contracts', [
                'WKM_id' => 7,
                'registratienummer' => $registratienummer,
            ]);

        if ($response->failed()) {
            Log::error('easyflex get_contracts call failed', [
                'responseData' => $response->json(),
                'params' => ['WKM_id' => 7, 'registratienummer' => $registratienummer],
                'httpCode' => $response->status(),
            ]);

            return null;
        }

        $body = json_decode($response->body());
        return $body->data ?? null;
    }

    public function getToken(): string
    {
        return FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.easyflex_service'));
    }

    public function getVacationHours($registratienummer)
    {
        $client = $this->connectDSEasyflex();
        $params = [
            'license' => config('services.easyflex.license'),
            'parameters' => ["registratienummer" => $registratienummer],
            'fields' => [],
        ];

        $reserveringen = $client->ds_fw_reserveringen($params)->fields;
        $vakantieuren = 0;
        $total_time = 0;

        foreach ($reserveringen as $index => $value) {
            if (is_array($value)) {
                foreach ($value as $item) {
                    if ($item->fw_res_lc_type === 20822) {
                        $total_time += $item->fw_res_saldo_tijd;
                        $vakantieuren += (float) bcdiv($item->fw_res_saldo_tijd, '60', 2);
                    }
                }
            } else {
                if ($value->fw_res_lc_type === 20822) {
                    $total_time += $value->fw_res_saldo_tijd;
                    $vakantieuren += (float) bcdiv($value->fw_res_saldo_tijd, '60', 2);
                }
            }
        }

        return $vakantieuren;
    }

    public function getDocumentStatus($documentnumber): string
    {
        $client = $this->connectDSEasyflex();
        $params = [
            'license' => config('services.easyflex.license'),
            'parameters' => ["nummer" => $documentnumber],
            'fields' => [],
        ];

        $documentStatus = $client->ds_wm_urenbestandgegevens($params)->fields;

        if (isset($documentStatus->item)) {
            $statusCode = $documentStatus->item->wm_urenbestandgegevens_status;

            return match ($statusCode) {
                26720 => 'Te verwerken', //oranje
                26721 => 'In bewerking', //oranje
                26722 => 'Deels verwerkt', //oranje
                26724 => 'Verwerkt (warning)',
                26723 => 'Verwerkt', //groen
                26729 => 'Fout', //rood
                default => 'Unknown status',
            };
        }

        return 'Geen gegevens gevonden';
    }

    public function sendToHoursEasyflex($fields)
    {
        $client = $this->connectEasyflex();
        $params = [
            'license' => config('services.easyflex.license'),
            'parameters' => $fields,
            'fields' => ['wm_urenimport_success' => '', 'wm_urenimport_files' => ''],
        ];

        try {
            $response = $client->wm_urenimport($params);

            return [
                'success' => true,
                'fields' => $response->fields,
            ];
        } catch (SoapFault $e) {
            $errorDetail = $e->detail->detail ?? $e->getMessage();

            return [
                'success' => false,
                'error' => $errorDetail,
            ];
        }
    }

    public function connectEasyflex(): SoapClient
    {
        return retry(
            times: 2,
            callback: static fn() => new SoapClient(config('services.easyflex.wsdl_url'), [
                'trace' => 2,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'soap_version' => SOAP_1_1,
            ]),
            sleepMilliseconds: 250
        );
    }

    public function connectDSEasyflex(): SoapClient
    {
        $license = config('services.easyflex.license');

        return retry(
            times: 2,
            callback: static fn () => new SoapClient(config('services.easyflex.url') . '/dataservice/tools/wsdl.tpsp?key=' . urlencode($license), [
                'trace' => 2,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'exceptions' => true,
                'soap_version' => SOAP_1_1,
            ]),
            sleepMilliseconds: 250);
    }
}
