<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;

class CommunicationService
{
    private $communicationClient;
    function __construct() {
        $this->communicationClient = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])
            ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.communicatie_service')))
            ->baseUrl(config('services.communicatie_service.url'))
            ->throw()
        ;
    }

    public function sendOffboardMessages($data)
    {
        // offboard email
        $this->communicationClient->post('/offboard', $data)->json();
        return true;
        // offboard whatsapp (only for firing)
//        $whatsappData = [
//            'phone_number' => $data['mobile_phone'],
//            'language' => $data['preferred_language'],
//            'provider' => 'joboti',
//        ];
//        $this->communicationClient->post('/whatsapp/offboard', $whatsappData)->json();
    }

    public function notifyThuisbezorgd($data)
    {
        $this->communicationClient->post('/notify-thuisbezorgd', $data)->json();
    }

    public function sendWorkPermitRequest($data)
    {
        $this->communicationClient->post('/whatsapp/work-permit-pending', $data)->json();
    }

    public function sendTwvToUser($data)
    {
        $this->communicationClient->post('/twv', $data)->json();
        return true;
    }

    public function sendResidencePermitReminder($data, $whatsappData)
    {
        $this->communicationClient->post('/residence-permit-renewal-reminder', $data)->json();
        $this->communicationClient->post('/whatsapp/residence-permit-renewal-reminder', $whatsappData)->json();
        return true;
    }

    public function sendContractRenewalConfirmation($data)
    {
        $this->communicationClient->post('/contract-renewal-confirmation', $data)->json();
        return true;
    }

    public function sendContractRenewalMessage($data)
    {
        // contract-renewal email
        $this->communicationClient->post('/contract-renewal', $data)->json();
        return true;
    }
}
