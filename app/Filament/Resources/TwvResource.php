<?php

namespace App\Filament\Resources;

use App\Filament\Actions\NotifyJetAction;
use App\Filament\Resources\TwvResource\Pages;
use App\Filament\Resources\TwvResource\RelationManagers;
use App\Models\Flexapp\User;
use App\Models\JetApplicant;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class TwvResource extends Resource
{
    protected static ?string $model = JetApplicant::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationLabel = "TWV's";
    protected static ?string $modelLabel = "TWV's";
    protected static ?string $pluralModelLabel = "TWV's";

    public static function getEloquentQuery(): Builder
    {
        $flexappIds = JetApplicant::select('flexapp_id')
            ->whereHas('enrollmentStatusNew', function(Builder $query) {
                $query->whereRaw('on_hold IS NOT true')
                    ->where('agency_status', 'Approved')
                    ->where('step_in_process', 16);
            })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding');
            })
            ->whereNotNull('flexapp_id')
            ->pluck('flexapp_id')
            ->toArray();


        return User::whereHas('info', function ($query) {
            $query->where('twv_approved', true)
                ->where('twv_expiration_date', '<', Carbon::today()->addWeeks(8));
        })
            ->join('info', 'jwt_users.uuid', '=', 'info.uuid')
            ->whereIn('info.uuid', $flexappIds)
            ->orderBy('info.twv_expiration_date')
            ->select('jwt_users.*')
            ->with(['info', 'personalData', 'jetApplicant']);

    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('personalData.first_name')
                    ->url(fn ($record): string => route('filament.admin.resources.jet-applicant-news.view-check', ['record' => $record->jetApplicant?->applicant_id]))
                    ->openUrlInNewTab()
                    ->searchable()
                    ->label('Name')
                    ->getStateUsing(function ($record) {
                        return $record->personalData?->first_name.' '.$record->personalData?->last_name;
                    }),
                Tables\Columns\TextColumn::make('jetApplicant.scoober_id')
                    ->label('Scoober ID'),
                Tables\Columns\TextColumn::make('jetApplicant.easyflex_id')
                    ->label('Easyflex ID'),
                Tables\Columns\TextColumn::make('flexappData.info.twv_expiration_date')
                    ->label('Expiration Date')
                    ->getStateUsing(function ($record) {
                        $date = $record->info?->twv_expiration_date;
                        return $date ? Carbon::createFromFormat('Y-m-d', $date)->format('d-m-Y') : null;
                    }),
                Tables\Columns\TextColumn::make('flexappData.info.twv_approved_date')
                    ->label('Approved Date')
                    ->getStateUsing(function ($record) {
                        $date = $record->info?->twv_approved_date;
                        return $date ? Carbon::parse($date)->format('d-m-Y') : null;
                    }),

                Tables\Columns\TextColumn::make('flexappData.info.twv_start_date')
                    ->label('Start Date')
                    ->getStateUsing(function ($record) {
                        $date = $record->info?->twv_start_date;
                        return $date ? Carbon::createFromFormat('Y-m-d', $date)->format('d-m-Y') : null;
                    })
            ])
            ->filters([
                //
            ])
            ->actions([
                NotifyJetAction::makeTable()
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTwvs::route('/'),
            'create' => Pages\CreateTwv::route('/create'),
            'edit' => Pages\EditTwv::route('/{record}/edit'),
        ];
    }
}
