<?php

namespace App\Filament\Resources\SignhostExpiredDocumentResource\Pages;

use App\Filament\Resources\SignhostExpiredDocumentResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Model;

class ListSignhostExpiredDocuments extends ListRecords
{
    protected static string $resource = SignhostExpiredDocumentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    public function getTableRecordKey(Model $record): string
    {
        return 'id';
    }
}
