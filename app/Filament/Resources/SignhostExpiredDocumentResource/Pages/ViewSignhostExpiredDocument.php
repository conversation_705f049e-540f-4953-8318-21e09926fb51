<?php

namespace App\Filament\Resources\SignhostExpiredDocumentResource\Pages;

use App\Filament\Resources\SignhostExpiredDocumentResource;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;

class ViewSignhostExpiredDocument extends ViewRecord
{
    protected static string $resource = SignhostExpiredDocumentResource::class;

    protected static ?string $navigationGroup = 'Controle';

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    public function form(Form $form): Form
    {
        $documents = $this->getRecord()->getAttribute('document');
        $sections = [];
        foreach ($documents as $index => $document) {
            $sections[] = Section::make('document: ' . data_get($document, 'file_name'))
                ->schema([
                    TextInput::make('document.' . $index . '.file_name')
                        ->readOnly(),
                    KeyValue::make('document.' . $index)
                        ->hiddenLabel()
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                ])
                ->collapsible()
                ->description('Information about the document');
        }

        return $form->schema([
            Section::make([
                TextInput::make('reference')
                    ->readOnly(),
                TextInput::make('status_text')
                    ->label('Status')
                    ->readOnly(),
                TextInput::make('transaction_id')
                    ->label('Signhost transaction')
                    ->readOnly(),
                TextInput::make('postback_url')
                    ->readOnly(),
            ])
                ->description('Information about the Signhost transaction'),
            ...$sections,
        ]);
    }
}
