<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostExpiredDocumentResource\Pages;
use App\Models\Filament\RemoteModel;
use App\Models\Filament\SignhostExpiredDocument;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\HigherOrderTapProxy;

class SignhostExpiredDocumentResource extends Resource
{
    protected static ?string $model = SignhostExpiredDocument::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('status_text'),
                Forms\Components\TextInput::make('transaction_id'),
                Forms\Components\TextInput::make('expired_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultGroup('transaction_id')
            ->columns([
                Tables\Columns\TextColumn::make('reference'),
                Tables\Columns\TextColumn::make('status_text'),
                Tables\Columns\TextColumn::make('transaction_id')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\IconColumn::make('send_email_notifications')->boolean(),
                Tables\Columns\IconColumn::make('seal')->boolean(),
                Tables\Columns\TextColumn::make('Expired at')
                    ->getStateUsing(fn (SignhostExpiredDocument $record) => $record
                        ->created_at->addDays($record->days_to_expire)
                        ->format('d-m-Y H:i:s')
                    ),
            ])
            ->paginated()
            ->deferLoading()
            ->filters([
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->options([
                                30 => 'Getekend',
                                40 => 'Afgekeurd',
                                50 => 'Verlopen',
                                60 => 'Geannuleerd',
                                70 => 'Failed',
                            ])
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query
                        ->when($status = data_get($data, 'status'), fn (Builder $query, $data) => $query->where('status', $status))
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    /**
     * @param int|string $key
     * @return HigherOrderTapProxy<Model>
     */
    public static function resolveRecordRouteBinding(int | string $key): ?Model
    {
        $model = new SignhostExpiredDocument();

        // Always fetch fresh data for single records to avoid Sushi cache issues
        // Skip the local cache lookup and go directly to remote API
        /** @var RemoteModel $model */
        $model->setQuery([$model->getRouteKeyName() => $key]);

        $data = $model->getData([$model->getRouteKeyName() => $key]);

        if ($data->isEmpty()) {
            throw new ModelNotFoundException("No query results for model [" . get_class($model) . "] {$key}");
        }

        // Apply transformation to the raw data before creating the model instance
        $transformedData = $data->map($model->transformSingleRecordData(...))->first();

        return $model->newInstance($transformedData, exists: true);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSignhostExpiredDocuments::route('/'),
            'view' => Pages\ViewSignhostExpiredDocument::route('/{record}'),
        ];
    }
}
