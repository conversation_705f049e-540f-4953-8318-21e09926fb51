<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JetApplicantResource\Pages;
use App\Http\Controllers\ContractController;
use App\Models\JetApplicant;
use App\Models\LooncomponentPercentage;
use App\Models\User;
use App\Services\EasyflexService;
use Carbon\CarbonInterface;
use Closure;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class JetApplicantResource extends Resource
{
    protected static ?string $model = JetApplicant::class;

    protected static bool $shouldRegisterNavigation = false;

    public static ?array $statusses = [
        0 => [
            'label' => 'Submit Lead',
            'color' => 'info',
        ],
        1 => [
            'label' => 'Receive Applicant',
            'color' => 'info',
        ],
        2 => [
            'label' => 'Send Registration',
            'color' => 'info',
        ],
        3 => [
            'label' => 'Finalize Registration',
            'color' => 'info',
        ],
        4 => [
            'label' => 'Verify ID',
            'color' => 'warning',
        ],
        5 => [
            'label' => 'Control Registration',
            'color' => 'warning',
        ],
        6 => [
            'label' => 'Sign Contract',
            'color' => 'info',
        ],
        7 => [
            'label' => 'Send Registration to JET',
            'color' => 'info',
        ],
        8 => [
            'label' => 'Finalize Easyflex Registration',
            'color' => 'warning',
        ],
        // Step 9 is a background step and will never be "step_in_process"
        9 => [
            'label' => 'Sync Scoober ID',
            'color' => 'info',
        ],
        10 => [
            'label' => 'Create Easyflex Job',
            'color' => 'success',
        ],
    ];

    public static ?array $renewStatusses = [
        0 => [
            'label' => 'Require decision',
            'color' => 'info',
        ],
        1 => [
            'label' => 'Residence permit required',
            'color' => 'warning',
        ],
        2 => [
            'label' => 'Residence permit requested',
            'color' => 'info',
        ],
        3 => [
            'label' => 'TWV required',
            'color' => 'warning',
        ],
        4 => [
            'label' => 'TWV requested',
            'color' => 'info',
        ],
        5 => [
            'label' => 'TWV approved',
            'color' => 'info',
        ],
        6 => [
            'label' => 'Contract sent',
            'color' => 'info',
        ],
        7 => [
            'label' => 'Contract signed',
            'color' => 'success',
        ],
        8 => [
            'label' => 'Contract declined',
            'color' => 'warning',
        ],
        9 => [
            'label' => 'Contract not signed',
            'color' => 'warning',
        ],
        10 => [
            'label' => 'Contract not renewed',
            'color' => 'danger',
        ],
        11 => [
            'label' => 'Renewal approved',
            'color' => 'success',
        ],
    ];

    public static ?array $reasons = [
        'Duplicated Applicant',
        'Found a new job',
        'Language barriers',
        'Lives too far from any starting location',
        'Missed Session',
        'Missing Documents',
        'Non-suitable Ex-Couriers',
        'No suitable contract type',
        'No suitable vehicle type',
        'Not compatible with other job',
        'Not immediately available',
        'Not interested anymore',
        'Other Reasons',
        'Recruitment target reached',
        'Safety Threat',
        'Salary expectations not met',
        'Test Applicants',
        'Under Legally Required Age',
        'Unmotivated',
        'Unresponsive',
        'Wants to work with External Agency',
        'Work Council not approved',
        'Work permit not valid',
        'Rejection of work permit due to CLA hourly wage',
        'Fraud',
    ];

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->getStateUsing(function ($record) {
                        if($record->flexapp_id && isset($record->flexappData->personalData)) {
                            return $record->flexappData->personalData->first_name . ' ' . $record->flexappData->personalData->last_name;
                        }

                        return $record->first_name . ' ' . $record->last_name;
                    })
                    ->searchable(['first_name', 'last_name', 'lead_id', 'scoober_id', 'wenote_id', 'email']),

                Tables\Columns\TextColumn::make('delivery_area')
                    ->label('Locatie')
                    ->searchable(['delivery_area'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->getStateUsing(function ($record) {
                        $startDate = $record->pre_contract_start_date;

                        if($record->flexapp_id && isset($record->flexappData->info)) {
                            $startDate = $record->flexappData->info->start_date;
                        }
                        return $startDate ? Carbon::createFromFormat('Y-m-d', $startDate)->format('d-m-Y') : null;
                    })
                    ->label('Startdatum'),

                Tables\Columns\TextColumn::make('phone')
                    ->getStateUsing(function($record) {
                        if($record->flexapp_id && isset($record->flexappData->personalData)) {
                            $phone = $record->flexappData->personalData->phone_number;

                            if($record->flexappData?->personalData?->country_code) {
                                $phone = '+' . $record->flexappData?->personalData?->country_code . $phone;
                            }

                            return $phone;
                        }

                        return $record->phone;
                    })
                    ->copyable()
                    ->label('Telefoon')
                    ->searchable(['phone']),

                Tables\Columns\TextColumn::make('pre_contract_created_date')
                    ->label('Procesduur')
                    ->getStateUsing(function ($record) {
                        $start = Carbon::parse($record->pre_contract_created_date);
                        $end = $record->enrollmentStatus->agency_status === 'New' ? Carbon::now() : Carbon::parse($record->enrollmentStatus->agency_status_updated_at);

                        return $start->locale('nl')->diffForHumans([
                            'other' => $end,
                            'syntax' => CarbonInterface::DIFF_ABSOLUTE,
                            'parts' => '2',
                            'join' => ' en ',
                        ]);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('enrollmentStatus.updated_at')
                    ->label('Statusduur')
                    ->getStateUsing(function ($record) {
                        if ($record->enrollmentStatus) {
                            if (
                                $record->enrollmentStatus->agency_status !== 'Rejected'
                                && ($record->enrollmentStatus->agency_status !== 'Approved' || $record->enrollmentStatus->step_in_process !== 10)
                            ) {
                                if ($record->enrollmentStatus->on_hold) {
                                    $start = Carbon::parse($record->enrollmentStatus->on_hold_started_at);
                                } else {
                                    $start = Carbon::parse($record->enrollmentStatus["step_{$record->enrollmentStatus->step_in_process}_started_at"]);
                                }

                                return $start->diffForHumans([
                                    'other' => Carbon::now(),
                                    'syntax' => CarbonInterface::DIFF_ABSOLUTE,
                                    'parts' => '2',
                                    'join' => ' en ',
                                ]);
                            }
                        }

                        return null;
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('enrollmentStatus.step_in_process') // TODO: Change to step_name
                    ->label('Status')
                    ->state(function (Model $record) {
                        if ($record->enrollmentStatus?->on_hold) {
                            return 'On hold';
                        }

                        if($record->enrollmentStatus->agency_status === 'Rejected') {
                            return $record->enrollmentStatus->agency_status;
                        } else if($record->enrollmentStatus->agency_status === 'Approved' && $record->enrollmentStatus->step_in_process === 10) {
                            return $record->enrollmentStatus->agency_status;
                        }

                        if ($record->flexappData?->info?->twv_required && $record->flexappData?->info?->twv_approved === null) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'TWV aangevraagd';
                            } else {
                                return 'TWV aanvraag vereist';
                            }
                        }

                        return JetApplicantResource::$statusses[$record->enrollmentStatus->step_in_process ?? 0]['label'];
                    })
                    ->badge()
                    ->color(function(Model $record) {
                        if ($record->enrollmentStatus?->on_hold) {
                            return 'danger';
                        }

                        if($record->enrollmentStatus->agency_status === 'Approved' && $record->enrollmentStatus->step_in_process === 10) {
                            return 'success';
                        } else if($record->enrollmentStatus->agency_status === 'Rejected') {
                            return 'danger';
                        }

                        if ($record->flexappData?->info?->twv_required && $record->flexappData?->info?->twv_approved === null) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'success';
                            } else {
                                return 'danger';
                            }
                        }

                        return JetApplicantResource::$statusses[$record->enrollmentStatus->step_in_process ?? 0]['color'];
                    })
                    ->tooltip(function(Model $record) {
                        if ($record->flexappData?->info?->twv_required && $record->flexappData?->info?->twv_approved === null) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'Twv aangevraagd op ' . Carbon::parse($record->flexappData->info->twv_requested_date)->locale('nl')->translatedFormat('j M Y');
                            }
                        }
                    })
                    ->sortable(),

                Tables\Columns\IconColumn::make('flexappData.info.whatsapp_consent')
                    ->label('')
                    ->icon('icon-whatsapp')
                    ->boolean()
                    ->tooltip(fn ($state) => ($state ? 'Wel' : 'Geen') . ' consent')
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('enrollmentStatus.notes')
                    ->label('')
                    ->icon('heroicon-o-chat-bubble-oval-left')
                    ->getStateUsing(function ($record) {
                        if (isset($record->enrollmentStatus->notes)) {
                            $notes = array_filter(explode('|SEP|', $record->enrollmentStatus->notes));

                            if (count($notes) > 0) {
                                return $notes[0];
                            }
                        }

                        return null;
                    })
                    ->wrap()
                    ->tooltip(fn ($state) => str_replace(['<p>', '</p>'], " ", $state))
                    ->extraAttributes(function ($record) {
                        if (isset($record->enrollmentStatus->notes)) {
                            $notes = array_filter(explode('|SEP|', $record->enrollmentStatus->notes));

                            if (count($notes) > 0) {
                                return [
                                    'class' => 'relative pe-6 text-gray-500 after:content-[attr(data-count)] after:absolute after:top-2/4 after:right-2 after:-translate-y-1/2',
                                    'data-count' => count($notes)
                                ];
                            }
                        }

                        return [];
                    })
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('flexappData.applicationProfile.language')
                    ->label('Taal')
                    ->getStateUsing(function ($record) {
                        if($record->flexapp_id && isset($record->flexappData->applicationProfile)) {
                            return $record->flexappData->applicationProfile->language;
                        }

                        return $record->preferred_language;
                    }),
            ])
            ->poll('15s')
            ->recordUrl(fn ($record) => Pages\ViewCheckJetApplicants::getUrl(['record' => $record]))
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(50)
            ->paginated([25, 50, 75, 100])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('Open in wenote')
                        ->url(fn ($record) => config('services.wenote.app_url') . '/beheer/kandidaten.php?command=display_kandidaat&id=' .$record?->wenote_id, shouldOpenInNewTab: true)
                        ->hidden(fn ($record) => !$record->wenote_id),
                ]),
            ])
            ->filters([
                Tables\Filters\Filter::make('enrollmentStatus')
                    ->form([
                        Select::make('step_in_process')
                            ->options(array_column(JetApplicantResource::$statusses, 'label'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if($data['step_in_process'] || $data['step_in_process'] === '0') {
                            return $query
                                ->when(
                                    $data,
                                    function (Builder $query, $data): Builder {
                                        return $query->orWhereRelation('enrollmentStatus', 'step_in_process', '=', $data['step_in_process']);
                                    },
                                );
                        }
                        return $query;
                    })
                    ->indicator('Status')
                    ->indicateUsing(function (array $data) {
                        if($data['step_in_process'] || $data['step_in_process'] === '0') {
                            return JetApplicantResource::$statusses[$data['step_in_process']]['label'];
                        }
                        return null;
                    }),
                Tables\Filters\Filter::make('delivery_area')
                    ->form([
                        Select::make('delivery_area')
                            ->multiple()
                            ->options(function () {
                                $options = array_filter(JetApplicant::all()->pluck('delivery_area', 'delivery_area')->toArray());
                                asort($options);

                                return $options;
                            })
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                !empty($data['delivery_area']),
                                function (Builder $query) use ($data): Builder {
                                    return $query->whereIn('delivery_area', $data['delivery_area']);
                                }
                            );
                    })
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [];
        // return [
        //     'index' => Pages\ListJetApplicants::route('/'),
        //     'index-new' => Pages\ListNewJetApplicants::route('/new'),
        //     'index-approved' => Pages\ListApprovedJetApplicants::route('/approved'),
        //     'index-rejected' => Pages\ListRejectedJetApplicants::route('/rejected'),
        //     'index-on-hold' => Pages\ListOnHoldJetApplicants::route('/on-hold'),
        //     'index-test' => Pages\ListTestJetApplicants::route('/test'),
        //     'index-ghost' => Pages\ListGhostRiders::route('/ghost-riders'),
        //     'view-check' => Pages\ViewCheckJetApplicants::route('/{record}/check'),
        // ];
    }
}
