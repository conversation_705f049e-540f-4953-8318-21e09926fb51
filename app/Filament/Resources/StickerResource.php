<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StickerResource\Pages;
use App\Filament\Resources\StickerResource\RelationManagers;
use App\Models\DatacheckerResultImage;
use App\Models\DatacheckerTransaction;
use App\Models\DatacheckerTransactionResult;
use App\Models\Flexapp\User;
use App\Models\JetApplicant;
use App\Models\Sticker;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class StickerResource extends Resource
{
    protected static ?string $model = JetApplicant::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationLabel = "Sticker's";
    protected static ?string $modelLabel = "Sticker's";
    protected static ?string $pluralModelLabel = "Sticker's";

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        $flexappIds = JetApplicant::whereHas('enrollmentStatusNew', function ($query) {
            $query->whereRaw('on_hold IS NOT true')
                ->where('agency_status', 'Approved')
                ->where('step_in_process', 16);
        })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding')
                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
                        $subQuery->whereDate('offboarding_date', '>', now());
                    });
            })
            ->pluck('flexapp_id')
            ->toArray();

        $usersWithStickers = DatacheckerTransaction::whereIn('user_id', $flexappIds)
            ->whereHas('datacheckerTransactionResult', function ($query) {
                $query->where('status', 'APPROVED')
                    ->whereHas('datacheckerResultImage', function ($q) {
                        $q->where('descriptor', 'RESIDENCE_PERMIT_STICKERS');
                    });
            })->pluck('user_id')
        ->toArray();

        $datacheckerIds = DatacheckerTransaction::selectRaw('distinct on (user_id) id')
            ->from('transactions as dt')
            ->whereIn('user_id', $usersWithStickers)
            ->where('status', 20)
            ->orderBy('user_id')
            ->orderByDesc('created_at')
            ->pluck('id')
            ->toArray();

        $stickerTransactions = DatacheckerTransaction::whereIn('transactions.id', $datacheckerIds)
            ->whereHas('datacheckerTransactionResult', function ($query) {
                $query->where('status', 'APPROVED')
                    ->whereHas('datacheckerResultImage', function ($q) {
                        $q->where('descriptor', 'RESIDENCE_PERMIT_STICKERS');
                    });
            })
            ->join('transaction_results', 'transactions.id', '=', 'transaction_results.transaction_id')
            ->where('transaction_results.status', 'APPROVED')
            ->orderByRaw("((transaction_results._raw_data #>> '{}')::jsonb->'rightToWork'->'data'->>'vsDueDate') ASC")
            ->select('transactions.*')
            ->with(['datacheckerTransactionResult.datacheckerResultImage' => fn ($q) =>
                $q->where('descriptor', 'RESIDENCE_PERMIT_STICKERS')
            ]);

        return $stickerTransactions;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('personalData.first_name')
                    ->url(fn ($record): string => route('filament.admin.resources.jet-applicant-news.view-check', ['record' => $record->jetApplicant?->applicant_id]))
                    ->openUrlInNewTab()
                    ->searchable()
                    ->label('Name')
                    ->getStateUsing(function ($record) {
                        return $record->jetApplicant->flexappData->personalData?->first_name.' '.$record->jetApplicant->flexappData->personalData?->last_name;
                    }),
                Tables\Columns\TextColumn::make('datacheckerTransactionResult.remote_result_id')
                    ->label('Result ID'),
                Tables\Columns\TextColumn::make('jetApplicant.scoober_id')
                    ->label('Scoober ID'),
                Tables\Columns\TextColumn::make('jetApplicant.easyflex_id')
                    ->label('Easyflex ID'),
                Tables\Columns\TextColumn::make('jetApplicant.datacheckerTransactionResult.raw_data')
                    ->label('From Date')
                    ->getStateUsing(function ($record) {
                        $data = json_decode(json_decode($record->datacheckerTransactionResult?->_raw_data));
                        $date = $data->rightToWork?->data?->vsFromDate;
                        return $date ? Carbon::parse($date)->format('d-m-Y') : null;
                    }),

                Tables\Columns\TextColumn::make('flexappData.info.twv_start_date')
                    ->label('End Date')
                    ->getStateUsing(function ($record) {
                        $data = json_decode(json_decode($record->datacheckerTransactionResult?->_raw_data));
                        $date = $data->rightToWork?->data?->vsDueDate;
                        return $date ? Carbon::parse($date)->format('d-m-Y') : null;
                    })
            ])
            ->filters([
                //
            ])
            ->actions([])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStickers::route('/'),
            'create' => Pages\CreateSticker::route('/create'),
            'edit' => Pages\EditSticker::route('/{record}/edit'),
        ];
    }
}
