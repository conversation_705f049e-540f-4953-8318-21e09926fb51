<?php

namespace App\Filament\Resources\LoonComponent;

use App\Filament\Resources\LoonComponent\LooncomponentPercentageResource\Pages;
use App\Models\LooncomponentPercentage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LooncomponentPercentageResource extends Resource
{
    protected static ?string $model = LooncomponentPercentage::class;
    protected static ?string $navigationLabel = 'Loon/Percentages';
    protected static ?string $navigationGroup = 'Loon info';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('age')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('function')
                    ->options([
                        'Courier' => 'Courier',
                        'Courier Captain' => 'Courier Captain'
                    ])
                    ->default('Courier')
                    ->required(),
                Forms\Components\TextInput::make('percentage')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('wage_per_hour')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('start_date')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('age')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('wage_per_hour')
                    ->money('EUR', locale: 'nl')
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->suffix('%')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('function')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date('d-m-Y')
                    ->sortable(),
            ])
            ->defaultSort(fn (Builder $query) => $query->orderBy('start_date', 'asc')->orderBy('age', 'asc'))
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLooncomponentPercentages::route('/'),
            'create' => Pages\CreateLooncomponentPercentage::route('/create'),
            'edit' => Pages\EditLooncomponentPercentage::route('/{record}/edit'),
        ];
    }
}
