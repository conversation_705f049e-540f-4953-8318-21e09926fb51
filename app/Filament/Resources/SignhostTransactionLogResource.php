<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostTransactionLogResource\Pages\ListSignhostTransactionLogs;
use App\Filament\Resources\SignhostTransactionLogResource\Pages\ViewSignhostTransactionLogs;
use App\Models\Filament\RemoteModel;
use App\Models\Filament\SignhostTransactionLog;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Toggle as FormToggle;
use Filament\Forms\Components\TextInput as FormTextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn as TableIconColumn;
use Filament\Tables\Columns\TextColumn as TableTextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class SignhostTransactionLogResource extends Resource
{
    protected static ?string $model = SignhostTransactionLog::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make([
                    Section::make([
                        FormTextInput::make('transaction_id')
                            ->readOnly(),
                        FormTextInput::make('transaction_uuid')
                            ->readOnly(),
                    ])->columns(),
                    Section::make([
                        FormTextInput::make('action')
                            ->readOnly(),
                        FormToggle::make('is_just_eat')
                            ->label('Just Eat')
                            ->disabled(),
                        FormToggle::make('status')
                            ->disabled(),
                    ])->columns(3),
                    KeyValue::make('transaction')->label('Signhost Transaction')
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                ]),
                Section::make([
                    KeyValue::make('form_data')
                        ->keyLabel('Field')
                        ->valueLabel('Value'),
                    KeyValue::make('files')
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                    KeyValue::make('signers')
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                    KeyValue::make('receivers')
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                    KeyValue::make('details')
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                ])
                    ->hiddenLabel(false)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
            TableTextColumn::make('transaction_id'),
            TableTextColumn::make('transaction_uuid'),
            TableTextColumn::make('action'),
            TableIconColumn::make('status')->boolean(),
            TableIconColumn::make('details'),
            TableIconColumn::make('is_just_eat')->boolean(),
        ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->bulkActions([
                //
            ]);
    }

    /**
     * @param int|string $key
     * @return Model|null
     */
    public static function resolveRecordRouteBinding(int | string $key): ?Model
    {
        $model = new SignhostTransactionLog();

        // Always fetch fresh data for single records to avoid Sushi cache issues
        // Skip the local cache lookup and go directly to remote API
        /** @var RemoteModel $model */
        $model->setQuery([$model->getRouteKeyName() => $key]);

        $data = $model->getData([$model->getRouteKeyName() => $key]);

        if ($data->isEmpty()) {
            throw new ModelNotFoundException("No query results for model [" . get_class($model) . "] {$key}");
        }

        // Apply transformation to the raw data before creating the model instance
        $transformedData = $data->map($model->transformSingleRecordData(...))->first();

        return $model->newInstance($transformedData, exists: true);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSignhostTransactionLogs::route('/'),
            'view' => ViewSignhostTransactionLogs::route('/{record}'),
        ];
    }
}
