<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostPostbackLogResource\Pages\ListSignhostPostbackLogs;
use App\Filament\Resources\SignhostPostbackLogResource\Pages\ViewSignhostPostbackLogs;
use App\Models\Filament\RemoteModel;
use App\Models\Filament\SignhostPostbackLog;
use Filament\Tables\Actions\ViewAction;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\HigherOrderTapProxy;

class SignhostPostbackLogResource extends Resource
{
    protected static ?string $model = SignhostPostbackLog::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('postback_id')
                    ->readOnly(),
                TextInput::make('transaction_uuid')
                    ->readOnly(),
                TextInput::make('action')
                    ->readOnly(),
                CheckBox::make('succeeded')
                    ->disabled(),
                TextInput::make('message')
                    ->readOnly(),
                KeyValue::make('postback')
                    ->keyLabel('Property')
                    ->valueLabel('Value'),
                KeyValue::make('transaction')
                    ->keyLabel('Property')
                    ->valueLabel('Value'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('postback_id'),
                TextColumn::make('transaction_uuid'),
                TextColumn::make('action'),
                IconColumn::make('succeeded')->boolean(),
            ])
            ->filters([
                Filter::make('succeeded')
                    ->query(fn (Builder $query): Builder => $query->where('succeeded', true))
                    ->toggle(),
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->bulkActions([
                //
            ]);
    }

    /**
     * @param int|string $key
     * @return HigherOrderTapProxy<Model>
     */
    public static function resolveRecordRouteBinding(int | string $key): ?Model
    {
        $model = new SignhostPostbackLog();

        // Always fetch fresh data for single records to avoid Sushi cache issues
        // Skip the local cache lookup and go directly to remote API
        /** @var RemoteModel $model */
        $model->setQuery([$model->getRouteKeyName() => $key]);

        $data = $model->getData([$model->getRouteKeyName() => $key]);

        if ($data->isEmpty()) {
            throw new ModelNotFoundException("No query results for model [" . get_class($model) . "] {$key}");
        }

        // Apply transformation to the raw data before creating the model instance
        $transformedData = $data->map($model->transformSingleRecordData(...))->first();

        return $model->newInstance($transformedData, exists: true);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSignhostPostbackLogs::route('/'),
            'view' => ViewSignhostPostbackLogs::route('/{record}'),
        ];
    }
}
