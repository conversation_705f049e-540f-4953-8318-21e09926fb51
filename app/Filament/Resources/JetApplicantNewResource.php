<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JetApplicantNewResource\Pages;
use App\Http\Controllers\ContractController;
use App\Models\JetApplicant;
use App\Models\JetEnrollmentStep;
use App\Models\JetApplicantEnrollmentStatusNew;
use App\Models\LooncomponentPercentage;
use App\Models\User;
use App\Services\EasyflexService;
use Carbon\CarbonInterface;
use Closure;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Filament\Tables\Contracts\HasTable;

class JetApplicantNewResource extends Resource
{
    protected static ?string $model = JetApplicant::class;

    protected static bool $shouldRegisterNavigation = false;

    public static ?array $renewStatusses = [
        0 => [
            'label' => 'Require decision',
            'color' => 'info',
        ],
        1 => [
            'label' => 'Residence permit required',
            'color' => 'warning',
        ],
        2 => [
            'label' => 'Residence permit requested',
            'color' => 'info',
        ],
        3 => [
            'label' => 'TWV required',
            'color' => 'warning',
        ],
        4 => [
            'label' => 'TWV requested',
            'color' => 'info',
        ],
        5 => [
            'label' => 'TWV approved',
            'color' => 'info',
        ],
        6 => [
            'label' => 'Contract sent',
            'color' => 'info',
        ],
        7 => [
            'label' => 'Contract signed',
            'color' => 'success',
        ],
        8 => [
            'label' => 'Contract declined',
            'color' => 'warning',
        ],
        9 => [
            'label' => 'Contract not signed',
            'color' => 'warning',
        ],
        10 => [
            'label' => 'Contract not renewed',
            'color' => 'danger',
        ],
        11 => [
            'label' => 'Renewal approved',
            'color' => 'success',
        ],
    ];

    public static ?array $reasons = [
        'Duplicated Applicant',
        'Found a new job',
        'Language barriers',
        'Lives too far from any starting location',
        'Missed Session',
        'Missing Documents',
        'Non-suitable Ex-Couriers',
        'No suitable contract type',
        'No suitable vehicle type',
        'Not compatible with other job',
        'Not immediately available',
        'Not interested anymore',
        'Other Reasons',
        'Recruitment target reached',
        'Safety Threat',
        'Salary expectations not met',
        'Test Applicants',
        'Under Legally Required Age',
        'Unmotivated',
        'Unresponsive',
        'Wants to work with External Agency',
        'Work Council not approved',
        'Work permit not valid',
        'Rejection of work permit due to CLA hourly wage',
        'Fraud',
    ];

    public static function table(Table $table): Table
    {
        $enrollmentSteps = JetEnrollmentStep::all();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->getStateUsing(function ($record) {
                        if($record->flexapp_id && isset($record->flexappData->personalData)) {
                            return $record->flexappData->personalData->first_name . ' ' . $record->flexappData->personalData->last_name;
                        }

                        return $record->first_name . ' ' . $record->last_name;
                    })
                    ->searchable(['first_name', 'last_name', 'lead_id', 'scoober_id', 'wenote_id', 'email']),

                Tables\Columns\TextColumn::make('delivery_area')
                    ->label('Locatie')
                    ->searchable(['delivery_area'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->getStateUsing(function ($record) {
                        $startDate = $record->pre_contract_start_date;

                        if($record->flexapp_id && isset($record->flexappData->info)) {
                            $startDate = $record->flexappData->info->start_date;
                        }
                        return $startDate ? Carbon::createFromFormat('Y-m-d', $startDate)->format('d-m-Y') : null;
                    })
                    ->label('Startdatum'),

                Tables\Columns\TextColumn::make('phone')
                    ->getStateUsing(function($record) {
                        if($record->flexapp_id && isset($record->flexappData->personalData)) {
                            $phone = $record->flexappData->personalData->phone_number;

                            if($record->flexappData?->personalData?->country_code) {
                                $phone = '+' . $record->flexappData?->personalData?->country_code . $phone;
                            }

                            return $phone;
                        }

                        return $record->phone;
                    })
                    ->copyable()
                    ->label('Telefoon')
                    ->searchable(['phone']),

                Tables\Columns\TextColumn::make('Procesduur')
                    ->label('Procesduur')
                    ->getStateUsing(function ($record) {
                        $enrollmentStartedAt = $record->enrollmentStatusNew->step_2_started_at;
                        $enrollmentFinishedAt = null;
                        if ($record->enrollmentStatusNew->agency_status !== 'New' && $record->enrollmentStatusNew->agency_status_updated_at) {
                            $enrollmentFinishedAt = $record->enrollmentStatusNew->agency_status_updated_at;
                        } else {
                            $enrollmentFinishedAt = Carbon::now();
                        }

                        return ($enrollmentStartedAt && $enrollmentFinishedAt) ? Carbon::parse($enrollmentStartedAt)
                            ->locale('nl')
                            ->diffForHumans([
                                'other' => $enrollmentFinishedAt,
                                'syntax' => CarbonInterface::DIFF_ABSOLUTE,
                                'parts' => '2',
                                'join' => ' en ',
                            ]) : ''
                        ;
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->addSelect([
                                'proccess_interval' => JetApplicantEnrollmentStatusNew::selectRaw(<<<SQL
                                (CASE
                                    WHEN agency_status_updated_at IS NOT NULL AND agency_status != 'New' THEN agency_status_updated_at
                                    ELSE NOW()::timestamp(0)
                                END)::timestamp(0) - step_2_started_at::timestamp(0) AS proccess_interval
                                SQL)
                                ->whereColumn('applicant_id', 'applicants.applicant_id')
                                ->limit(1)
                            ])
                            ->orderBy('proccess_interval', $direction)
                        ;
                    })
                ,

                Tables\Columns\TextColumn::make('Statusduur')
                    ->label('Statusduur')
                    ->state(function (JetApplicant $record) {
                        $stepStartedAt = $record->enrollmentStatusNew["step_{$record->enrollmentStatusNew->step_in_process}_started_at"];
                        $stepFinishedAt = null;
                        if (
                            $record->enrollmentStatusNew->on_hold ||
                            $record->enrollmentStatusNew->agency_status === 'Rejected' ||
                            $record->enrollmentStatusNew->agency_status === 'Approved' && $record->enrollmentStatusNew->step_in_process === 16
                        ) {
                            $stepFinishedAt = null;
                        } else if ($record->enrollmentStatusNew["step_{$record->enrollmentStatusNew->step_in_process}_finished_at"] ?? null) {
                            $stepFinishedAt = $record->enrollmentStatusNew["step_{$record->enrollmentStatusNew->step_in_process}_finished_at"];
                        } else {
                            $stepFinishedAt = Carbon::now();
                        }

                        return ($stepStartedAt && $stepFinishedAt) ?
                            Carbon::parse($stepStartedAt)
                            ->locale('nl')
                            ->diffForHumans([
                                'other' => $stepFinishedAt,
                                'syntax' => CarbonInterface::DIFF_ABSOLUTE,
                                'parts' => 2,
                                'join' => ' en ',
                            ]) : ''
                        ;
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->addSelect([
                                'step_started_at' => JetApplicantEnrollmentStatusNew::selectRaw(<<<SQL
                                CASE
                                    WHEN step_in_process = 1 THEN step_1_started_at
                                    WHEN step_in_process = 2 THEN step_2_started_at
                                    WHEN step_in_process = 3 THEN step_3_started_at
                                    WHEN step_in_process = 4 THEN step_4_started_at
                                    WHEN step_in_process = 5 THEN step_5_started_at
                                    WHEN step_in_process = 6 THEN step_6_started_at
                                    WHEN step_in_process = 7 THEN step_7_started_at
                                    WHEN step_in_process = 8 THEN step_8_started_at
                                    WHEN step_in_process = 9 THEN step_9_started_at
                                    WHEN step_in_process = 10 THEN step_10_started_at
                                    WHEN step_in_process = 11 THEN step_11_started_at
                                    WHEN step_in_process = 12 THEN step_12_started_at
                                    WHEN step_in_process = 13 THEN step_13_started_at
                                    WHEN step_in_process = 14 THEN step_14_started_at
                                    WHEN step_in_process = 15 THEN step_15_started_at
                                    WHEN step_in_process = 16 THEN step_16_started_at
                                END step_started_at
                                SQL)
                                ->whereColumn('applicant_id', 'applicants.applicant_id')
                                ->limit(1)
                            ])
                            ->orderBy('step_started_at', $direction)
                        ;
                    })
                    ->visible(static fn (HasTable $livewire) => class_basename($livewire) === 'ListNewJetApplicants')
                ,

                Tables\Columns\TextColumn::make('enrollmentStatusNew.step_in_process') // TODO: Change to step_name
                    ->label('Status')
                    ->state(function (Model $record) use ($enrollmentSteps) {
                        if ($record->enrollmentStatusNew?->on_hold) {
                            return 'On hold';
                        }

                        if($record->enrollmentStatusNew->agency_status === 'Rejected') {
                            return $record->enrollmentStatusNew->agency_status;
                        } else if($record->enrollmentStatusNew->agency_status === 'Approved' && $record->enrollmentStatusNew->step_in_process === 16) {
                            return $record->enrollmentStatusNew->agency_status;
                        }

                        if (
                            $record->flexappData?->info?->twv_required &&
                            $record->flexappData?->info?->twv_approved === null &&
                            ($record->enrollmentStatusNew->step_in_process ?? 1) >= 10
                            ) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'TWV aangevraagd';
                            } else {
                                return 'TWV aanvraag vereist';
                            }
                        }

                        return $enrollmentSteps->find($record->enrollmentStatusNew->step_in_process ?? 1)['step_name'];
                    })
                    ->badge()
                    ->color(function(Model $record) use ($enrollmentSteps) {
                        if ($record->enrollmentStatusNew?->on_hold) {
                            return 'danger';
                        }

                        if($record->enrollmentStatusNew->agency_status === 'Approved' && $record->enrollmentStatusNew->step_in_process === 16) {
                            return 'success';
                        } else if($record->enrollmentStatusNew->agency_status === 'Rejected') {
                            return 'danger';
                        }

                        if (
                            $record->flexappData?->info?->twv_required &&
                            $record->flexappData?->info?->twv_approved === null &&
                            ($record->enrollmentStatusNew->step_in_process ?? 1) >= 10
                            ) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'success';
                            } else {
                                return 'danger';
                            }
                        }
                        return $enrollmentSteps->find($record->enrollmentStatusNew->step_in_process ?? 1)['step_color'];
                    })
                    ->tooltip(function(Model $record) {
                        if ($record->flexappData?->info?->twv_required && $record->flexappData?->info?->twv_approved === null) {
                            if ($record->flexappData?->info?->twv_requested) {
                                return 'Twv aangevraagd op ' . Carbon::parse($record->flexappData->info->twv_requested_date)->locale('nl')->translatedFormat('j M Y');
                            }
                        }
                    })
                    ->sortable(),

                Tables\Columns\IconColumn::make('flexappData.info.whatsapp_consent')
                    ->label('')
                    ->icon('icon-whatsapp')
                    ->boolean()
                    ->tooltip(fn ($state) => ($state ? 'Wel' : 'Geen') . ' consent')
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('enrollmentStatusNew.notes')
                    ->label('')
                    ->icon('heroicon-o-chat-bubble-oval-left')
                    ->getStateUsing(function ($record) {
                        if (isset($record->enrollmentStatusNew->notes)) {
                            $notes = array_filter(explode('|SEP|', $record->enrollmentStatusNew->notes));

                            if (count($notes) > 0) {
                                return $notes[0];
                            }
                        }

                        return null;
                    })
                    ->wrap()
                    ->tooltip(fn ($state) => str_replace(['<p>', '</p>'], " ", $state))
                    ->extraAttributes(function ($record) {
                        if (isset($record->enrollmentStatusNew->notes)) {
                            $notes = array_filter(explode('|SEP|', $record->enrollmentStatusNew->notes));

                            if (count($notes) > 0) {
                                return [
                                    'class' => 'relative pe-6 text-gray-500 after:content-[attr(data-count)] after:absolute after:top-2/4 after:right-2 after:-translate-y-1/2',
                                    'data-count' => count($notes)
                                ];
                            }
                        }

                        return [];
                    })
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('flexappData.applicationProfile.language')
                    ->label('Taal')
                    ->getStateUsing(function ($record) {
                        if($record->flexapp_id && isset($record->flexappData->applicationProfile)) {
                            return $record->flexappData->applicationProfile->language;
                        }

                        return $record->preferred_language;
                    }),
            ])
            ->poll('15s')
            ->recordUrl(fn ($record) => Pages\ViewCheckJetApplicants::getUrl(['record' => $record]))
            ->defaultSort(static fn (HasTable $livewire) => class_basename($livewire) === 'ListNewJetApplicants' ? 'Statusduur' : 'applicant_id', 'desc')
            ->defaultPaginationPageOption(50)
            ->paginated([25, 50, 75, 100])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('Open in wenote')
                        ->url(fn ($record) => config('services.wenote.app_url') . '/beheer/kandidaten.php?command=display_kandidaat&id=' .$record?->wenote_id, shouldOpenInNewTab: true)
                        ->hidden(fn ($record) => !$record->wenote_id),
                ]),
            ])
            ->filters([
                Tables\Filters\Filter::make('enrollmentStatusNew')
                    ->form([
                        Select::make('step_in_process')
                            ->multiple()
                            ->options(array_column($enrollmentSteps->toArray(), 'step_name', 'step_id'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $stepsInProcess = $data['step_in_process'] ?? [];

                        if($stepsInProcess) {
                            return $query->whereHas('enrollmentStatusNew', function (Builder $query) use ($stepsInProcess) {
                                $query->whereIn('step_in_process', $stepsInProcess);
                            });
                        }

                        return $query;
                    })
                    ->indicateUsing(function (array $data) use ($enrollmentSteps) {
                        $stepsInProcess = $data['step_in_process'] ?? [];
                        if ($stepsInProcess) {
                            return Tables\Filters\Indicator::make('Step in process')->removeField('step_in_process');
                        }
                    })
                ,
                Tables\Filters\Filter::make('delivery_area')
                    ->form([
                        Select::make('delivery_area')
                            ->multiple()
                            ->options(function () {
                                $options = array_filter(JetApplicant::all()->pluck('delivery_area', 'delivery_area')->toArray());
                                asort($options);

                                return $options;
                            })
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                !empty($data['delivery_area']),
                                function (Builder $query) use ($data): Builder {
                                    return $query->whereIn('delivery_area', $data['delivery_area']);
                                }
                            );
                    })
                    ->indicateUsing(function (array $data) {
                        $stepsInProcess = $data['delivery_area'] ?? [];
                        if ($stepsInProcess) {
                            return Tables\Filters\Indicator::make('Delivery area')->removeField('delivery_area');
                        }
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJetApplicants::route('/'),
            'index-new' => Pages\ListNewJetApplicants::route('/new'),
            'index-approved' => Pages\ListApprovedJetApplicants::route('/approved'),
            'index-rejected' => Pages\ListRejectedJetApplicants::route('/rejected'),
            'index-on-hold' => Pages\ListOnHoldJetApplicants::route('/on-hold'),
            'index-test' => Pages\ListTestJetApplicants::route('/test'),
            'index-ghost' => Pages\ListGhostRiders::route('/ghost-riders'),
            'view-check' => Pages\ViewCheckJetApplicants::route('/{record}/check'),
        ];
    }
}
