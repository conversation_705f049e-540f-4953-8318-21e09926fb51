<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostApiRequestLogResource\Pages;
use App\Models\Filament\RemoteModel;
use App\Models\Filament\SignhostApiRequestLog;
use App\Models\Filament\SushiEloquentBuilder;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class SignhostApiRequestLogResource extends Resource
{
    protected static ?string $model = SignhostApiRequestLog::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('transaction_id')
                    ->label('Signhost transaction')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('stage')
                    ->badge()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('method')
                    ->label('HTTP method')
                    ->formatStateUsing(fn (string $state) => strtoupper($state))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('response_code')
                    ->numeric()
                    ->badge()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->label('Excecuted at')
                    ->sortable(),
            ])
            ->defaultGroup('transaction_id')
            ->filters([
                SelectFilter::make('stage')
                    ->modifyBaseQueryUsing(function (SushiEloquentBuilder $query, $state) {
                        if (! $state['value']) {
                            return $query;
                        }

                        return $query->where('stage', $state['value']);
                    })
                    ->options([
                        'make_request' => 'make_request',
                        'make_response' => 'make_response',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    /**
     * @param int|string $key
     * @return Model|null
     */
    public static function resolveRecordRouteBinding(int | string $key): ?Model
    {
        $model = new SignhostApiRequestLog();

        // Always fetch fresh data for single records to avoid Sushi cache issues
        // Skip the local cache lookup and go directly to remote API
        /** @var RemoteModel $model */
        $model->setQuery([$model->getRouteKeyName() => $key]);

        $data = $model->getData([$model->getRouteKeyName() => $key]);

        if ($data->isEmpty()) {
            throw new ModelNotFoundException("No query results for model [" . get_class($model) . "] {$key}");
        }

        // Apply transformation to the raw data before creating the model instance
        $transformedData = $data->map($model->transformSingleRecordData(...))->first();

        return $model->newInstance($transformedData, exists: true);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSignhostApiRequestLog::route('/'),
            'view' => Pages\ViewSignhostApiRequestLog::route('/{record}'),
        ];
    }
}
