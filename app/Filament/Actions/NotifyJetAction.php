<?php

namespace App\Filament\Actions;

use App\Models\Plaatsing;
use App\Services\CommunicationService;
use Filament\Actions\Action as FilamentAction;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;

class NotifyJetAction
{
    /**
     * Get the form schema for the notify JET action
     */
    protected static function getFormSchema(): array
    {
        return [
            Forms\Components\Select::make('reason')
                ->label('Reden')
                ->options([
                    'expired_document' => 'Expired document',
                    'no_document_uploaded' => 'No new document uploaded'
                ])
                ->required(),
            Forms\Components\Select::make('permit_type')
                ->label('Type document')
                ->options([
                    'residence permit' => 'Verblijfsvergunning',
                    'work permit' => 'TWV',
                    'sticker' => 'Sticker'
                ])
                ->required(),
            Forms\Components\DatePicker::make('date')
                ->label('Datum (optioneel)'),
        ];
    }

    /**
     * Configure common action properties
     */
    protected static function configureAction($action): mixed
    {
        return $action
            ->label('JET informeren')
            ->requiresConfirmation()
            ->color('success')
            ->form(static::getFormSchema());
    }

    /**
     * Create action for use in header actions (ActionGroup context)
     */
    public static function make(): FilamentAction
    {
        return static::configureAction(FilamentAction::make('notifyJet'))
            ->action(function (array $data, CommunicationService $communicationService, $livewire) {
                $record = $livewire->getRecord();
                static::handleNotifyJet($record, $data, $communicationService);
            });
    }

    /**
     * Create action for use in table actions
     */
    public static function makeTable(): TableAction
    {
        return static::configureAction(TableAction::make('notifyJet'))
            ->icon('heroicon-m-envelope')
            ->button()
            ->action(function ($record, array $data, CommunicationService $communicationService) {
                static::handleNotifyJet($record, $data, $communicationService);
            });
    }

    protected static function handleNotifyJet($record, $data, CommunicationService $communicationService): void
    {
        try {
            // Get the JetApplicant - either the record itself or via relationship
            $jetApplicant = static::getJetApplicant($record);

            // Validate required data
            if (!$jetApplicant->scoober_id) {
                Notification::make()
                    ->title('Fout bij versturen notificatie')
                    ->body('Geen Scoober ID gevonden voor deze applicant.')
                    ->danger()
                    ->persistent()
                    ->send();
                return;
            }

            $plaatsingen = Plaatsing::where('applicant_id', $jetApplicant->applicant_id)
                ->with('hub')
                ->whereNull('offboard_date')
                ->get();
            $data['hub_emails'] = $plaatsingen->pluck('hub.email_address')->unique()->values()->toArray();

            // Add scoober_id from the jet applicant
            $data['scoober_id'] = $jetApplicant->scoober_id;

            if(!is_null($data['date'])){
                $data['document_expire_date'] = $data['date'];
            }else{
                // Determine document expiry date based on permit type
                $data['document_expire_date'] = static::getDocumentExpiryDate($record, $data['permit_type']);
            }

            // Add applicant personal data
            $data['first_name'] = $jetApplicant->flexappData?->personalData?->first_name;
            $data['last_name'] = $jetApplicant->flexappData?->personalData?->last_name;

            // Validate that we have the required personal data
            if (!$data['first_name'] || !$data['last_name']) {
                Notification::make()
                    ->title('Fout bij versturen notificatie')
                    ->body('Geen volledige naam gevonden voor deze applicant.')
                    ->danger()
                    ->persistent()
                    ->send();
                return;
            }

            // Send notification to Thuisbezorgd
            $communicationService->notifyThuisbezorgd($data);

            // Show success notification
            $permitTypeLabel = match($data['permit_type']) {
                'residence permit' => 'Verblijfsvergunning',
                'work permit' => 'TWV',
                'sticker' => 'Sticker',
                default => $data['permit_type']
            };

            $reasonLabel = match($data['reason']) {
                'expired_document' => 'verlopen document',
                'no_document_uploaded' => 'geen nieuw document geüpload',
                default => $data['reason']
            };

            $bodyText = "Notificatie verstuurd voor {$data['first_name']} {$data['last_name']} - {$permitTypeLabel} ({$reasonLabel})";

            if (!empty($data['document_expire_date'])) {
                $bodyText .= "\nVervaldatum: {$data['document_expire_date']}";
            }

            Notification::make()
                ->title('JET notificatie verstuurd')
                ->body($bodyText)
                ->success()
                ->persistent()
                ->send();

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('NotifyJetAction error: ' . $e->getMessage(), [
                'record_class' => get_class($record),
                'record_id' => $record->id ?? 'unknown',
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            // Show error notification to user
            Notification::make()
                ->title('Fout bij versturen notificatie')
                ->body('Er is een fout opgetreden bij het versturen van de notificatie naar JET. Probeer het opnieuw of neem contact op met de beheerder.')
                ->danger()
                ->persistent()
                ->send();
        }
    }

    protected static function getJetApplicant($record)
    {
        // If the record is already a JetApplicant, return it
        if ($record instanceof \App\Models\JetApplicant) {
            return $record;
        }

        // If the record has a jetApplicant relationship, use that
        if (isset($record->jetApplicant)) {
            return $record->jetApplicant;
        }

        throw new \Exception('Unable to find JetApplicant from the provided record');
    }

    protected static function getDocumentExpiryDate($record, string $permitType): ?string
    {
        $jetApplicant = static::getJetApplicant($record);

        switch ($permitType) {
            case 'TWV':
                // For JetApplicant records, get the most recent TWV information
                $twvInfo = $jetApplicant->twvInformation()->orderBy('created_at', 'desc')->first();
                $expiryDate = $twvInfo?->twv_expiration_date;
                return $expiryDate ? Carbon::parse($expiryDate)->format('Y-m-d') : null;

            case 'sticker':
                // For JetApplicant records, get the most recent datachecker transaction
                $transaction = $jetApplicant->dataCheckerTransaction()
                    ->where('document_type', 'residencePermit')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($transaction) {
                    $rawJson = $transaction->datacheckerTransactionResult?->_raw_data;
                    $decodedLevel1 = $rawJson ? json_decode($rawJson) : null;
                    $rawData = $decodedLevel1 ? json_decode($decodedLevel1) : null;
                    $stickerEndDate = $rawData?->rightToWork?->data?->vsDueDate;
                    return $stickerEndDate ? Carbon::parse($stickerEndDate)->format('Y-m-d') : null;
                }
                return null;

            case 'verblijfsvergunning':
            default:
                // For JetApplicant records, get the most recent datachecker transaction
                $transaction = $jetApplicant->dataCheckerTransaction()
                    ->where('document_type', 'residencePermit')
                    ->orderBy('created_at', 'desc')
                    ->first();

                return $transaction?->datacheckerTransactionResult?->datacheckerIdentityDocument?->date_of_expiry;
        }
    }
}
