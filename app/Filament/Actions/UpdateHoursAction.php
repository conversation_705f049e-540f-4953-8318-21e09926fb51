<?php

namespace App\Filament\Actions;

use App\Services\CommunicationService;
use App\Services\EasyflexService;
use Filament\Actions\Action as FilamentAction;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;

class UpdateHoursAction
{
    /**
     * Get the form schema for the notify JET action
     */
    protected static function getFormSchema(): array
    {
        return [
            Forms\Components\TextInput::make('new_weekly_hours')
                ->label('Nieuwe uren per week')
                ->required(),
        ];
    }

    protected static function configureAction($action): mixed
    {
        return $action
            ->label('Uren updaten')
            ->requiresConfirmation()
            ->color('success')
            ->form(static::getFormSchema());
    }

    /**
     * Create action for use in header actions (ActionGroup context)
     */
    public static function make(): FilamentAction
    {
        return static::configureAction(FilamentAction::make('updateHours'))
            ->action(function (array $data, EasyflexService $easyflexService, $livewire) {
                $record = $livewire->getRecord();
                dd($data);
                $easyflexService->updateHours($record, $data);
            });
    }

}
