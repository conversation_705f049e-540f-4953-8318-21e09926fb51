<?php

namespace App\Console\Commands;

use App\Models\EasyflexPlaatsing;
use App\Models\EmployeeOffboarding;
use App\Models\JetApplicant;
use Carbon\Carbon;
use Illuminate\Console\Command;

class OffboardEasyflexUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:offboard-easyflex-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $applicants = JetApplicant::whereNotNull('scoober_id')
            ->whereNot('applicant_id', 91)
            ->whereNotNull('easyflex_id')
            ->whereHas('enrollmentStatusNew', function ($query) {
                $query->whereRaw('on_hold IS NOT true')
                    ->where('agency_status', 'Approved')
                    ->where('step_in_process', 16);
            })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding');
            })->get();

        foreach($applicants as $applicant){
            $plaatsingen = $applicant->plaatsing;
            if ($plaatsingen->isEmpty() || $plaatsingen->count() > 1) {
                continue;
            }

            $plaatsing_ids = $plaatsingen->pluck('plaatsing_id')->toArray();
            $allPast = true;
            $latestEindDatum = null;

            foreach($plaatsingen as $plaatsing){
                $eindDatum = null;
                $easyflexPlaatsing = EasyflexPlaatsing::where('rl_plaatsingen_plaatsingnummer', $plaatsing->plaatsing_id)->first();

                if (
                    $easyflexPlaatsing &&
                    !is_null($easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum)
                ) {
                    $eindDatum = Carbon::parse($easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum);
                    $latestEindDatum = $eindDatum;
                    if ($eindDatum->isPast()) {
                        $plaatsing->offboard_date = $easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum;
                        $plaatsing->save();
                        $this->info("Updated offboard_date {$easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum} for plaatsing {$plaatsing->plaatsing_id}.");
                    } else {
                        $allPast = false;
                    }
                } else {
                    $allPast = false;
                }
            }

            if ($allPast && $latestEindDatum) {
                EmployeeOffboarding::create([
                    'applicant_id' => $applicant->applicant_id,
                    'reason' => 'offboarding_sync',
                    'offboarding_date' => $latestEindDatum,
                    'plaatsing_ids' => $plaatsing_ids,
                    'is_completed' => true,
                    'processed_reserveringen' => true,
                    'processed_in_uwv' => true,
                    'signalering' => true,
                    'contract_id' => 0,
                    'user_id' => 1,
                ]);
                $this->info("User offboarded {$applicant->applicant_id}.");
            }
        }
    }
}
