<?php

namespace App\Console\Commands;

use App\Models\JetApplicant;
use App\Models\TwvInformation;
use App\Models\DatacheckerTransaction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelWriter;

class ExportTwvResidenceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:export-twv-residence-data {--limit=} {--filename=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export TWV and residence permit data for applicants with Verblijfsvergunning documents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting TWV and residence permit data export...');

        // Generate filename
        $filename = $this->option('filename') ?? 'twv_residence_export_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';
        $limit = $this->option('limit');

        // Ensure exports directory exists
        Storage::disk('local')->makeDirectory('exports');
        $filePath = storage_path('app/exports/' . $filename);

        // Create CSV writer
        $csv = SimpleExcelWriter::create($filePath);

        // Add CSV headers
        $csv->addHeader([
            'Scoober ID',
            'Easyflex ID',
            'Registration certificate valid until',
            'Residence title until'
        ]);

        // Query applicants with necessary relationships
        $query = JetApplicant::whereNotNull('scoober_id')
            ->whereNotNull('flexapp_id')
            ->whereRaw('is_test IS NOT true')
            ->with([
                'twvInformation',
                'dataCheckerTransaction.datacheckerTransactionResult.datacheckerIdentityDocument'
            ]);

        if ($limit) {
            $query->limit((int) $limit);
        }

        $totalApplicants = $query->count();
        $this->info("Processing {$totalApplicants} applicants...");

        $progressBar = $this->output->createProgressBar($totalApplicants);
        $progressBar->start();

        $processedCount = 0;
        $errorCount = 0;

        // Get all applicants and process them
        $applicants = $query->get();

        foreach ($applicants as $applicant) {
            try {
                $row = $this->processApplicant($applicant);
                if ($row) { // Only add row if applicant has Verblijfsvergunning
                    $csv->addRow($row);
                    $processedCount++;
                }
            } catch (\Exception $e) {
                $this->error("Error processing applicant {$applicant->applicant_id}: " . $e->getMessage());
                $errorCount++;
            }
            $progressBar->advance();
        }

        $csv->close();
        $progressBar->finish();
        $this->newLine();

        $this->info("Export completed!");
        $this->info("File saved to: {$filePath}");
        $this->info("Processed: {$processedCount} applicants with Verblijfsvergunning");

        if ($errorCount > 0) {
            $this->warn("Errors encountered: {$errorCount} applicants");
        }

        return 0;
    }

    /**
     * Process a single applicant and return CSV row data
     */
    private function processApplicant(JetApplicant $applicant): ?array
    {
        // Check if applicant has Verblijfsvergunning document
        $residencePermitData = $this->getResidencePermitData($applicant);

        if (!$residencePermitData) {
            return null; // Skip applicants without Verblijfsvergunning
        }

        // Get scoober_id
        $scooberId = $applicant->scoober_id;
        $easyflexId = $applicant->easyflex_id;

        // Get latest TWV expiration date
        $latestTwvExpirationDate = $this->getLatestTwvExpirationDate($applicant);

        // Get residence title valid until date
        $residenceTitleValidUntil = $residencePermitData['valid_until'];

        return [
            $scooberId,
            $easyflexId,
            $latestTwvExpirationDate,
            $residenceTitleValidUntil
        ];
    }

    /**
     * Get the latest TWV expiration date from TwvInformation
     */
    private function getLatestTwvExpirationDate(JetApplicant $applicant): string
    {
        $latestTwv = $applicant->twvInformation()
            ->whereNotNull('twv_expiration_date')
            ->orderBy('twv_expiration_date', 'desc')
            ->first();

        if ($latestTwv && $latestTwv->twv_expiration_date) {
            return Carbon::parse($latestTwv->twv_expiration_date)->format('d-m-Y');
        }

        return '';
    }

    /**
     * Get residence permit data from DataChecker transactions
     * Only returns data for Verblijfsvergunning documents
     */
    private function getResidencePermitData(JetApplicant $applicant): ?array
    {
        if (!$applicant->flexapp_id) {
            return null;
        }

        try {
            // Get the most recent approved DataChecker transaction with residence permit
            $transaction = $applicant->dataCheckerTransaction()
                ->where('status', 20) // Completed status
                ->whereHas('datacheckerTransactionResult', function ($query) {
                    $query->where('status', 'APPROVED')
                        ->whereHas('datacheckerIdentityDocument', function ($q) {
                            $q->where('document_type', 'RESIDENCE_PERMIT');
                        });
                })
                ->with(['datacheckerTransactionResult.datacheckerIdentityDocument'])
                ->latest()
                ->first();

            if (!$transaction || !$transaction->datacheckerTransactionResult) {
                return null;
            }

            $identityDocument = $transaction->datacheckerTransactionResult->datacheckerIdentityDocument;

            if (!$identityDocument) {
                return null;
            }

            // Check if document type is Verblijfsvergunning (RESIDENCE_PERMIT)
            if ($identityDocument->document_type !== 'RESIDENCE_PERMIT') {
                return null;
            }

            // Get the valid until date (date_of_expiry)
            $validUntil = '';
            if ($identityDocument->date_of_expiry) {
                $validUntil = Carbon::parse($identityDocument->date_of_expiry)->format('d-m-Y');
            }

            return [
                'document_type' => $identityDocument->document_type,
                'valid_until' => $validUntil
            ];

        } catch (\Exception $e) {
            $this->warn("Could not fetch residence permit data for applicant {$applicant->applicant_id}: " . $e->getMessage());
            return null;
        }
    }
}
