<?php

namespace App\Console\Commands;

use App\Models\DatacheckerTransaction;
use App\Models\Flexapp\UserIdentification;
use App\Models\JetApplicant;
use App\Models\ResidencePermitRenewal;
use App\Services\CommunicationService;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CheckResidencePermitStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-residence-permit-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if residence permits expire within 8 weeks';

    public function handle(EasyflexService $easyflexService, CommunicationService $communicationService)
    {
        // check all users with residence permit when expires
        $jetApplicants = JetApplicant::whereNotNull('easyflex_id')
            ->where(function ($query) {
                $query->where('is_test', false)
                    ->orWhereNull('is_test');
            })
            ->whereHas('enrollmentStatusNew', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where([
                        ['agency_status', '=', 'Approved'],
                        ['step_in_process', '=', 16]
                    ])
                    ;
            })
            ->whereDoesntHave('employeeOffboarding')
            ->get();

        $this->info(count($jetApplicants));

        $today = Carbon::today()->format('Y-m-d');
        $dateIn8Weeks = Carbon::today()->addWeeks(8)->format('Y-m-d');
        foreach($jetApplicants as $applicant)
        {
            $verblijfsvergunning = $applicant->flexappData?->identification?->document_type;
            if($verblijfsvergunning === 'Verblijfsvergunning'){
                // get all persoonsgegevens
                try {
                    $persoonsgegevens = $easyflexService->getPersoonsgegevens($applicant->easyflex_id);
                } catch (\Exception $e)
                {
                    $this->error('Person not found '.$applicant->easyflex_id);
                }

                if($persoonsgegevens->fw_persoonsgegevens_flexwerkerstatus == 21357 || $persoonsgegevens->fw_persoonsgegevens_flexwerkerstatus == 21358){
                    continue;
                }
                $documentType = $persoonsgegevens->fw_persoonsgegevens_idbewijs;
                $documentExpireDate = Carbon::parse($persoonsgegevens->fw_persoonsgegevens_idbewijsgeldigtot->date);
                $easyflexDocumentTypes = [21584, 21585, 21586, 21587, 21588];
//                $easyflexDocumentTypes = [21583];
                if (in_array($documentType, $easyflexDocumentTypes)) {
                    $data = [
//                        'email' => $applicant->flexappData?->personalData?->email,
                        'email' => '<EMAIL>',
                        'name' => $applicant->flexappData?->personalData?->first_name.' '.$applicant->flexappData?->personalData?->last_name,
                        'expiration_date' => $documentExpireDate->format('Y-m-d'),
                    ];

                    $whatsappData = [
                        'language' => 'en',
//                        'phone_number' => $applicant->flexappData?->personalData?->country_code.''.$applicant->flexappData?->personalData?->phone_number
                        'phone_number' => 31622619492
                    ];

                    if($documentExpireDate->between($today, $dateIn8Weeks)){
                        $residencePermit = $record->residencePermitRenewal ?? new ResidencePermitRenewal([
                            'applicant_id' => $applicant->applicant_id
                        ]);

                        $communicationService->sendResidencePermitReminder($data, $whatsappData);
                        $reminders = $residencePermit->reminder_dates ?? [];
                        $reminders[] = now();
                        $residencePermit->reminder_dates = $reminders;
                        $residencePermit->save();
                        dd('xxx');
                    }

                    if($documentExpireDate->isToday()){
                        //offboard user!
                        dd('xxx');
                    }
                }
            }
        }
    }
}
