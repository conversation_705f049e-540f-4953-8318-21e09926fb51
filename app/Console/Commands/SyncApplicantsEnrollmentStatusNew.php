<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Models\JetApplicant;
use App\Models\JetApplicantEnrollmentStatusNew;

class SyncApplicantsEnrollmentStatusNew extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:applicants-enrollment-status-new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs the ApplicantsEnrollmentStatusNew from the previous ApplicantsEnrollmentStatus.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $enrollmentStepsMatrix = [
            // $fromStepNumber => $toStepNumber
            0 => 1, // 'Submit Lead' => 'Submit Lead',
            1 => 2, // 'Receive Applicant' => 'Receive Applicant',
            2 => 3, // 'Send Registration' => 'Send Registration',
            // null => 4, // 'step_name' => 'Activate Account',
            3 => 5, // 'Finalize Registration' => 'Finalize Profile',
            // null => 6, // 'step_name' => 'Scan ID',
            // null => 7, // 'step_name' => 'Verify ID (DataChecker)',
            // null => 8, // 'step_name' => 'Scan Driver License',
            // null => 9, // 'step_name' => 'Verify Driver License (DataChecker)',
            4 => 10, // 'Verify ID' => 'Verify Scans',
            5 => 11, // 'Control Registration' => 'Control Registration',
            6 => 12, // 'Sign Contract' => 'Sign Contract',
            7 => 13, // 'Send Registration to JET' => 'Send Registration to JET',
            8 => 14, // 'Finalize Easyflex Registration' => 'Finalize Easyflex Registration',
            9 => 15, // 'Sync Scoober ID' => 'Fetch Scoober ID',
            10 => 16, // 'Create Easyflex Job' => 'Create Easyflex Job',
        ];

        foreach(JetApplicant::orderBy('applicant_id')->get() as $jetApplicant) {
            $enrollmentStatus = $jetApplicant->enrollmentStatus;
            $enrollmentStatusNew = [
                'applicant_enrollment_status_id' => $enrollmentStatus->applicant_id,
                'applicant_id' => $enrollmentStatus->applicant_id,
                'created_at' => $enrollmentStatus->created_at,
                'updated_at' => $enrollmentStatus->updated_at,
                'agency_status' => $enrollmentStatus->agency_status,
                'agency_status_reason' => $enrollmentStatus->agency_status_reason,
                'agency_status_updated_at' => $enrollmentStatus->agency_status_updated_at,
                'on_hold' => $enrollmentStatus->on_hold,
                'on_hold_reason' => $enrollmentStatus->on_hold_reason,
                'on_hold_started_at' => $enrollmentStatus->on_hold_started_at,
                'on_hold_finished_at' => $enrollmentStatus->on_hold_finished_at,
                'notes' => $enrollmentStatus->notes,
                'step_in_process' => $enrollmentStepsMatrix[$enrollmentStatus->step_in_process],
                'step_name' => $enrollmentStatus->step_name
            ];
            foreach ($enrollmentStepsMatrix as $fromStepNumber => $toStepNumber) {
                if ($fromStepNumber === 3 && !$enrollmentStatus->step_3_finished_at) {
                    $enrollmentStatusNew['step_4_started_at'] = $enrollmentStatus->step_3_started_at;
                    $enrollmentStatusNew['step_4_finished_at'] = null;
                    $enrollmentStatusNew['step_in_process'] = 4;
                } else {
                    $enrollmentStatusNew['step_' . $toStepNumber . '_started_at'] = $enrollmentStatus->{'step_' . $fromStepNumber . '_started_at'};
                    $enrollmentStatusNew['step_' . $toStepNumber . '_finished_at'] = $enrollmentStatus->{'step_' . $fromStepNumber . '_finished_at'};
                }
            }
            JetApplicantEnrollmentStatusNew::withoutTimestamps(fn () => $jetApplicant->enrollmentStatusNew()->create($enrollmentStatusNew));
        }
        JetApplicantEnrollmentStatusNew::selectRaw("setval('applicants_enrollment_status__applicant_enrollment_status_i_seq'::regclass, max(applicant_enrollment_status_id))")->get();
    }
}
