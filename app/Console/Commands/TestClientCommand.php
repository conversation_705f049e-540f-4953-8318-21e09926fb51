<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\EasyflexContracts;
use App\Models\EmployeeOffboarding;
use App\Models\Filament\SignhostExpiredDocument;
use App\Models\JetApplicant;
use App\Models\Plaatsing;
use App\Services\CommunicationService;
use Illuminate\Console\Command;
use Illuminate\Http\Client\Factory;
use Illuminate\Http\Client\PendingRequest;

final class TestClientCommand extends Command
{
    protected $signature = 'app:test-client';

    protected $description = 'Command to test random stuff';

    public function handle(): void
    {
        try {
            $model = \App\Models\Filament\SignhostExpiredDocument::find(255);
            dd($model->toArray());
        } catch (\Exception $e) {
            dump($e->getMessage());
        }
    }
}
