<?php

namespace App\Console\Commands;

use App\Filament\Resources\SignhostApiRequestLogResource;
use App\Models\Filament\RemoteModel;
use App\Models\Filament\SignhostExpiredDocument;
use App\Models\Filament\SignhostApiRequestLog;
use Illuminate\Console\Command;

class TestSingleRecordRetrieval extends Command
{
    protected $signature = 'test:single-record {id?}';
    protected $description = 'Test single record retrieval functionality';

    public function handle()
    {
        $this->info('Testing Single Record Retrieval');
        $this->info('================================');
        $this->newLine();

        $testId = $this->argument('id') ?? '123';

        // Test models that support single record retrieval
//        $this->testModel(SignhostExpiredDocument::class, $testId, 'Expired Document');

        // Test models that don't support single record retrieval
        $this->testModel(SignhostApiRequestLog::class, $testId, 'API Request Log');

        $this->newLine();
        $this->info('Test completed.');
    }

    /** @param class-string<RemoteModel> $modelClass */
    private function testModel(string $modelClass, string $id, string $modelName)
    {
        $this->info("Testing {$modelName} (ID: {$id}):");

        try {
            $model = new $modelClass();

            $this->info("  Single record URL: " . $model->remoteSingleUrl($id));

            // Test find method
            $this->info("  Testing find() method...");
            $result = $model->find($id);

            if ($result) {
                $this->info("  ✓ Record found");
                $this->info("  ✓ Model class: " . get_class($result));
                $this->info("  ✓ Exists flag: " . ($result->exists ? 'true' : 'false'));

                // Show some attributes if available
                $attributes = $result->getAttributes();
                if (!empty($attributes)) {
                    $this->info("  ✓ Sample attributes: " . json_encode($attributes));
                }
            } else {
                $this->warn("  ⚠ No record found (this is expected if ID doesn't exist)");
            }

            // Test findOrFail method
            $this->info("  Testing findOrFail() method...");
            try {
                $model->findOrFail($id);
                $this->info("  ✓ findOrFail succeeded");
            } catch (\Exception $e) {
                $this->warn("  ⚠ findOrFail threw exception: " . $e->getMessage());
            }

        } catch (\Exception $e) {
            $this->error("  ✗ Error testing {$modelName}: " . $e->getMessage());
            $this->error("  Stack trace: " . $e->getTraceAsString());
        }

        $this->newLine();
    }
}
