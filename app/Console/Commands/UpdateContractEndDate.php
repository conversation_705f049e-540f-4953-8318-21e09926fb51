<?php

namespace App\Console\Commands;

use App\Models\EasyflexPlaatsing;
use App\Models\JetApplicant;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateContractEndDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-contract-end-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $easyflex;

    public function __construct()
    {
        parent::__construct();
        $this->easyflex = new EasyflexService();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        $plaatsingen = EasyflexPlaatsing::where('WKM_id', 7)->whereNull('rl_plaatsingen_plaatsingeinddatum')->groupBy('easyflex_registratienummer')->pluck('easyflex_registratienummer');

        $applicants = JetApplicant::whereHas('plaatsing', function ($query) {
                $query->whereNull('offboard_date');
            })
            ->whereNotNull('scoober_id')
            ->whereNotNull('easyflex_id')
//            ->whereIn('easyflex_id', $plaatsingen)
            ->whereHas('enrollmentStatusNew', function ($query) {
                $query->whereRaw('on_hold IS NOT true')
                    ->where('agency_status', 'Approved')
                    ->where('step_in_process', 16);
            })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding')
                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
                        $subQuery->whereDate('offboarding_date', '>', now());
                    });
            })
            ->with([
                'plaatsing' => function ($query) {
                    $query->whereNull('offboard_date');
                },
                'flexappData.info'
            ])
            ->orderByDesc('applicant_id')
            ->get();

        foreach($applicants as $applicant){
            $plaatsingen = $applicant->plaatsing;

            $contractEndDate = $applicant->flexappData?->info?->end_date;
            $twvExpirationDate = $applicant->flexappData?->info?->twv_expiration_date;

            $residencePermitEndDate = optional(
                $applicant->latestResidencePermitTransaction()?->datacheckerTransactionResult?->datacheckerIdentityDocument
            )->date_of_expiry;

            if(!$residencePermitEndDate && $applicant->flexappData?->identification?->document_type == 'Verblijfsvergunning'){
                $date = trim($applicant->flexappData?->identification->identification_valid_until);
                $residencePermitEndDate = Carbon::createFromFormat('d-m-Y', $date);
            }

            $rawJson = optional($applicant->getLatestStickerInformation()?->datacheckerTransactionResult)?->_raw_data;
            $decodedLevel1 = $rawJson ? json_decode($rawJson) : null;
            $rawData = $decodedLevel1 ? json_decode($decodedLevel1) : null;
            $stickerEndDate = optional($rawData?->rightToWork?->data)->vsDueDate;

            $dates = collect([
                $twvExpirationDate ? Carbon::parse($twvExpirationDate) : null,
                $contractEndDate ? Carbon::parse($contractEndDate) : null,
                $residencePermitEndDate ? Carbon::parse($residencePermitEndDate) : null,
                $stickerEndDate ? Carbon::parse($stickerEndDate) : null,
            ])->filter();

            $earliestDate = $dates->min();

            if($earliestDate) {
                foreach($plaatsingen as $plaatsing){
                    $easyflexPlaatsing = EasyflexPlaatsing::where('rl_plaatsingen_plaatsingnummer', $plaatsing->plaatsing_id)->first();
                    if(!$easyflexPlaatsing){
                        $this->error("Plaatsing niet gevonden {$plaatsing->plaatsing_id}");
                        continue;
                    }

                    if ($easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum) {
                        $einddatum = Carbon::parse($easyflexPlaatsing->rl_plaatsingen_plaatsingeinddatum);

                        if ($einddatum->equalTo($earliestDate)) {
//                            $this->info("Plaatsing {$plaatsing->plaatsing_id} closes on the same date as earliestDate: {$einddatum}");
                            continue;
                        }
                    }

                    $params = [
                        'rlregistratienummer' => 3153557,
                        'WKM_id' => 7,
                        'registratienummer' => $applicant->easyflex_id,
                        'plaatsingnummer' => $plaatsing->plaatsing_id,
                        'einddatumreden' => 54136,
                        'einddatum' => $earliestDate->format('Y-m-d'),
                        'einddatumstatus' => 24140,
                        'einddatumredenuwv' => 23199
                    ];

                    $endPlaatsing = $this->easyflex->updatePlaatsingEinde($params);
                    if (!$endPlaatsing || $endPlaatsing['message'] !== 'success') {
                        $this->error('Plaatsing not updated: ' . $plaatsing->plaatsing_id);
                    }else{
                        $this->info("Plaatsing closed: {$plaatsing->plaatsing_id} for {$applicant->easyflex_id} at {$earliestDate->format('Y-m-d')}");
                    }
                }
            }else{
                $this->error("Geen enkele datum bekend");
            }
        }
    }
}
