<?php
namespace App\Actions;

use App\Models\JetApplicant;
use Illuminate\Support\Carbon;

class JetApplicantActions {
    public static function addNote(JetApplicant|int $jetApplicant, string $note) {
        if (is_int($jetApplicant)) {
            $jetApplicant = JetApplicant::findOrFail($jetApplicant);
        }
        $date = Carbon::now('Europe/Amsterdam')->format('H:i d-m-Y');
        $notes = explode('|SEP|', $jetApplicant->enrollmentStatusNew->notes);

        if ($jetApplicant->enrollmentStatus) {
            $jetApplicant->enrollmentStatus->notes = implode('|SEP|', [
                '<p>' . $date . '</p>' . $note,
                ...$notes
            ]);
            $jetApplicant->enrollmentStatus->save();
        }
        #region New
        $jetApplicant->enrollmentStatusNew->notes = implode('|SEP|', [
            '<p>' . $date . '</p>' . $note,
            ...$notes
        ]);
        $jetApplicant->enrollmentStatusNew->save();
        #endregion
    }

    public static function removeNote(JetApplicant|int $jetApplicant, int $noteIndex) {
        if (is_int($jetApplicant)) {
            $jetApplicant = JetApplicant::findOrFail($jetApplicant);
        }
        $notes = explode('|SEP|', $jetApplicant->enrollmentStatusNew->notes);

        unset($notes[$noteIndex]);

        if ($jetApplicant->enrollmentStatus) {
            $jetApplicant->enrollmentStatus->notes = implode('|SEP|', $notes);
            $jetApplicant->enrollmentStatus->save();
        }
        #region New
        $jetApplicant->enrollmentStatusNew->notes = implode('|SEP|', $notes);
        $jetApplicant->enrollmentStatusNew->save();
        #endregion
    }
}
