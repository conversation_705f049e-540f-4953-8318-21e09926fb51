<?php
namespace App\Actions;

use App\Actions\JetApplicantActions;
use App\Actions\JetApplicantEnrollmentActions;
use App\Livewire\JetApplicantsContractStatuses;
use App\Models\EasyflexUser;
use App\Models\LooncomponentPercentage;
use App\Services\FusionauthService;
use App\Services\JetService;
use App\Services\UserService;
use App\Models\JetApplicant;
use App\Models\WenoteUser;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class JetActions {
    #region Applicant Actions
    private static array $alpha3IsoCodes = [
        "keine <PERSON>" => null,
        "Afghanistan" => "AFG",
        "Albania" => "ALB",
        "Algeria" => "DZA",
        "American Samoa" => "ASM",
        "Andorra" => "AND",
        "Angola" => "AGO",
        "Anguilla" => "AIA",
        "Antigua & Barbuda" => "ATG",
        "Argentina" => "ARG",
        "Armenia" => "ARM",
        "Aruba" => "ABW",
        "Australia" => "AUS",
        "Austria" => "AUT",
        "Azerbaijan" => "AZE",
        "The Bahamas" => "BHS",
        "Bahrain" => "BHR",
        "Bangladesh" => "BGD",
        "Barbados" => "BRB",
        "Belarus" => "BLR",
        "Belgium" => "BEL",
        "Belize" => "BLZ",
        "Benin" => "BEN",
        "Bermuda" => "BMU",
        "Bhutan" => "BTN",
        "Bolivia" => "BOL",
        "Bosnia & Herzegovina" => "BIH",
        "Botswana" => "BWA",
        "Brazil" => "BRA",
        "British Virgin Islands" => "VGB",
        "Brunei" => "BRN",
        "Bulgaria" => "BGR",
        "Burkina Faso" => "BFA",
        "Burma" => "MMR",
        "Burundi" => "BDI",
        "Cambodia" => "KHM",
        "Cameroon" => "CMR",
        "Canada" => "CAN",
        "Cape Verde" => "CPV",
        "Cayman Islands" => "CYM",
        "Central African Republic" => "CAF",
        "Chad" => "TCD",
        "Chile" => "CHL",
        "China" => "CHN",
        "Colombia" => "COL",
        "Comoros" => "COM",
        "Democratic Republic of the Congo" => "COD",
        "Republic of the Congo" => "COG",
        "Cook Islands" => "COK",
        "Costa Rica" => "CRI",
        "Cote d'Ivoire" => "CIV",
        "Croatia" => "HRV",
        "Cuba" => "CUB",
        "Cyprus" => "CYP",
        "Czech Republic" => "CZE",
        "Denmark" => "DNK",
        "Djibouti" => "DJI",
        "Dominica" => "DMA",
        "Dominican Republic" => "DOM",
        "East Timor" => "TLS",
        "Ecuador" => "ECU",
        "Egypt" => "EGY",
        "El Salvador" => "SLV",
        "Equatorial Guinea" => "GNQ",
        "Eritrea" => "ERI",
        "Estonia" => "EST",
        "Ethiopia" => "ETH",
        "Faroe Islands" => "FRO",
        "Fiji" => "FJI",
        "Finland" => "FIN",
        "France" => "FRA",
        "French Guiana" => "GUF",
        "French Polynesia" => "PYF",
        "Gabon" => "GAB",
        "The Gambia" => "GMB",
        "Gaza Strip" => "PSE",
        "Georgia" => "GEO",
        "Germany" => "DEU",
        "Ghana" => "GHA",
        "Greece" => "GRC",
        "Greenland" => "GRL",
        "Grenada" => "GRD",
        "Guadeloupe" => "GLP",
        "Guam" => "GUM",
        "Guatemala" => "GTM",
        "Guernsey" => "GGY",
        "Guinea" => "GIN",
        "Guinea-Bissau" => "GNB",
        "Guyana" => "GUY",
        "Haiti" => "HTI",
        "Honduras" => "HND",
        "Hong Kong" => "HKG",
        "Hungary" => "HUN",
        "Iceland" => "ISL",
        "India" => "IND",
        "Indonesia" => "IDN",
        "Iran" => "IRN",
        "Iraq" => "IRQ",
        "Ireland" => "IRL",
        "Isle of Man" => "IMN",
        "Israel" => "ISR",
        "Italy" => "ITA",
        "Jamaica" => "JAM",
        "Japan" => "JPN",
        "Jersey" => "JEY",
        "Jordan" => "JOR",
        "Kazakhstan" => "KAZ",
        "Kenya" => "KEN",
        "Kiribati" => "KIR",
        "North Korea" => "PRK",
        "South Korea" => "KOR",
        "Kuwait" => "KWT",
        "Kyrgyzstan" => "KGZ",
        "Laos" => "LAO",
        "Latvia" => "LVA",
        "Lebanon" => "LBN",
        "Lesotho" => "LSO",
        "Liberia" => "LBR",
        "Libya" => "LBY",
        "Liechtenstein" => "LIE",
        "Lithuania" => "LTU",
        "Luxembourg" => "LUX",
        "Macau" => "MAC",
        "Macedonia" => "MKD",
        "Madagascar" => "MDG",
        "Malawi" => "MWI",
        "Malaysia" => "MYS",
        "Maldives" => "MDV",
        "Mali" => "MLI",
        "Malta" => "MLT",
        "Marshall Islands" => "MHL",
        "Martinique" => "MTQ",
        "Mauritania" => "MRT",
        "Mauritius" => "MUS",
        "Mayotte" => "MYT",
        "Mexico" => "MEX",
        "The Federated States of Micronesia" => "FSM",
        "Moldova" => "MDA",
        "Monaco" => "MCO",
        "Mongolia" => "MNG",
        "Montenegro" => "MNE",
        "Montserrat" => "MSR",
        "Morocco" => "MAR",
        "Mozambique" => "MOZ",
        "Namibia" => "NAM",
        "Nauru" => "NRU",
        "Nepal" => "NPL",
        "Netherlands" => "NLD",
        "Netherlands Antilles" => "ANT",
        "New Caledonia" => "NCL",
        "New Zealand" => "NZL",
        "Nicaragua" => "NIC",
        "Niger" => "NER",
        "Nigeria" => "NGA",
        "Northern Mariana Islands" => "MNP",
        "Norway" => "NOR",
        "Oman" => "OMN",
        "Pakistan" => "PAK",
        "Palau" => "PLW",
        "Panama" => "PAN",
        "Papua New Guinea" => "PNG",
        "Paraguay" => "PRY",
        "Peru" => "PER",
        "Philippines" => "PHL",
        "Poland" => "POL",
        "Portugal" => "PRT",
        "Puerto Rico" => "PRI",
        "Qatar" => "QAT",
        "Reunion" => "REU",
        "Romania" => "ROU",
        "Russia" => "RUS",
        "Rwanda" => "RWA",
        "Saint Helena" => "SHN",
        "Saint Kitts & Nevis" => "KNA",
        "Saint Lucia" => "LCA",
        "St Pierre & Miquelon" => "SPM",
        "Saint Vincent and the Grenadines" => "VCT",
        "Samoa" => "WSM",
        "San Marino" => "SMR",
        "Sao Tome & Principe" => "STP",
        "Saudi Arabia" => "SAU",
        "Senegal" => "SEN",
        "Serbia" => "SRB",
        "Seychelles" => "SYC",
        "Sierra Leone" => "SLE",
        "Singapore" => "SGP",
        "Slovakia" => "SVK",
        "Slovenia" => "SVN",
        "Solomon Islands" => "SLB",
        "Somalia" => "SOM",
        "South Africa" => "ZAF",
        "Spain" => "ESP",
        "Sri Lanka" => "LKA",
        "Sudan" => "SDN",
        "Suriname" => "SUR",
        "Swaziland" => "SWZ",
        "Sweden" => "SWE",
        "Switzerland" => "CHE",
        "Syria" => "SYR",
        "Taiwan" => "TWN",
        "Tajikistan" => "TJK",
        "Tanzania" => "TZA",
        "Thailand" => "THA",
        "Togo" => "TGO",
        "Tonga" => "TON",
        "Trinidad & Tobago" => "TTO",
        "Tunisia" => "TUN",
        "Turkey" => "TUR",
        "Turkmenistan" => "TKM",
        "Turks & Caicos Is" => "TCA",
        "Tuvalu" => "TUV",
        "Uganda" => "UGA",
        "Ukraine" => "UKR",
        "United Arab Emirates" => "ARE",
        "United Kingdom" => "GBR",
        "United States" => "USA",
        "Uruguay" => "URY",
        "Uzbekistan" => "UZB",
        "Vanuatu" => "VUT",
        "Venezuela" => "VEN",
        "Vietnam" => "VNM",
        "Virgin Islands" => "VIR",
        "Wallis and Futuna" => "WLF",
        "West Bank" => "PSE",
        "Western Sahara" => "ESH",
        "Yemen" => "YEM",
        "Zambia" => "ZMB",
        "Zimbabwe" => "ZWE"
    ];

    private static function explodePhone (?string $phone): ?array {
        $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();

        if (!$phone || !$phoneUtil->isPossibleNumber($phone, 'NL')) {
            return null;
        }

        $phoneNumberObject = $phoneUtil->parse($phone, 'NL');

        return [
            $phoneNumberObject->getCountryCode(),
            $phoneNumberObject->getNationalNumber()
        ];
    }

    public static function isDrivingLicenseRequired (string $activeTransportationType): ?bool {
        $drivingLicenseRequiredList = ['Motorcycle / Scooter', 'E-Roller', 'Car / Kombi', 'Buffer Vehicle'];
        $drivingLicenseNotRequiredList = ['Bike', 'E-Bike'];
        $drivingLicenseRequired = in_array($activeTransportationType, $drivingLicenseRequiredList) ? true : null;
        $drivingLicenseRequired ??= in_array($activeTransportationType, $drivingLicenseNotRequiredList) ? false : null;
        return $drivingLicenseRequired;
    }

    public static function timeApplicantEnrollmentStep (int $applicantId, int $stepNumber, bool $finished = false, bool $skipStepInProcess = false): string {
        if (!($stepNumber >= 0 && $stepNumber <= 10)) {
            throw new \Exception('The stepNumber is not valid.', 400);
        }

        $enrollmentStatus = JetApplicant::findOrFail($applicantId)->enrollmentStatus;
        $nowDateTimeString = Carbon::now()->toDateTimeString();
        if ($enrollmentStatus) {
            if (!$skipStepInProcess) { $enrollmentStatus->step_in_process = $stepNumber; }
            $enrollmentStatus->{'step_' . $stepNumber . '_started_at'} ??= $nowDateTimeString;
            $enrollmentStatus->{'step_' . $stepNumber . '_finished_at'} = ($finished) ? $nowDateTimeString : null;

            $enrollmentStatus->save();
        }
        return $nowDateTimeString;
    }

    public static function resetApplicantEnrollmentSteps (int $applicantId, int $stepNumber, bool $finished = false, bool $skipStepInProcess = false): string {

    }

    public function cancelAllContracts($flexapp_id)
    {
        $response = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])->get(config('services.signhost_service.url') . '/get_local_transaction/' . $flexapp_id)->json();

        if(isset($response['data'])){
            foreach($response['data'] as $contract){
                dd($contract);
            }
        }
    }

    public static function timeApplicantRenewalStep (int $applicantId, int $stepNumber, bool $finished = false, bool $skipStepInProcess = false) {
        if (!($stepNumber >= 0 && $stepNumber <= 11)) {
            throw new \Exception('The stepNumber is not valid.', 400);
        }

        $applicant = JetApplicant::findOrFail($applicantId);

        if (!$applicant->renewalStatus) {
            $applicant->renewalStatus()->create([
                'applicant_id' => $applicantId,
                'step_in_process' => $stepNumber,
                'step_' . $stepNumber . '_started_at' => Carbon::now()->toDateTimeString(),
                'step_' . $stepNumber . '_finished_at' => ($finished) ? Carbon::now()->toDateTimeString() : null,
            ]);
        }else{
            $renewalStatus = $applicant->renewalStatus;
            if (!$skipStepInProcess) { $renewalStatus->step_in_process = $stepNumber; }
            $renewalStatus->{'step_' . $stepNumber . '_started_at'} ??= Carbon::now()->toDateTimeString();
            $renewalStatus->{'step_' . $stepNumber . '_finished_at'} = ($finished) ? Carbon::now()->toDateTimeString() : null;
            $renewalStatus->save();
        }
    }

    public static function getUurloon($startdate, $function, $dob)
    {
        $dob = \Carbon\Carbon::parse($dob);
        $startdate = Carbon::parse($startdate);
        $requestedAge = (int) $dob->diffInYears($startdate);
        $format_startdate = Carbon::parse($startdate)->format('Y-m-d');

        $age = ($requestedAge > 21) ? 21 : $requestedAge;
        $loonInfo = LooncomponentPercentage::where('age', $age)
            ->where('function', $function)
            ->whereDate('start_date', '<=', $format_startdate)
            ->orderBy('start_date', 'desc')->first();

        if ($loonInfo) {
            return $loonInfo->wage_per_hour;
        } else {
            return "No applicable wage found.";
        }
    }


    public static function syncNewApplicant (array $newApplicant): JetApplicant {
        # No longer needed when syncing
        unset(
            $newApplicant['agency_status'],
            $newApplicant['agency_status_reason']
        );

        $newJetApplicant = JetApplicant::where('email', $newApplicant['email'])->first();
        $nowDateTimeString = Carbon::now()->toDateTimeString();
        if ($newJetApplicant) {
            JetApplicantEnrollmentActions::resetAllSteps($newJetApplicant->applicant_id);
            $newJetApplicant->is_import = null;
            $newJetApplicant->save();
            $newJetApplicant->update($newApplicant);
            $newJetApplicant->enrollmentStatusNew()->update([
                'agency_status' => 'New',
                'agency_status_reason' => null,
                'agency_status_updated_at' => $nowDateTimeString,
                'step_in_process' => 2,
                'step_1_started_at' => $newApplicant['lead_created_date'],
                'step_1_finished_at' => $newApplicant['pre_contract_created_date'],
                'step_2_started_at' => $nowDateTimeString,
            ]);
            JetApplicantActions::addNote($newJetApplicant, '<strong>Let op!:</strong> Applicant opnieuw ingediend door JET.');
        } else {
            $newJetApplicant = JetApplicant::create($newApplicant);
            $newJetApplicant->enrollmentStatusNew()->create([
                'agency_status' => 'New',
                'step_in_process' => 2,
                'step_1_started_at' => $newApplicant['lead_created_date'],
                'step_1_finished_at' => $newApplicant['pre_contract_created_date'],
                'step_2_started_at' => $nowDateTimeString,
            ]);
        }

        JetApplicantEnrollmentActions::finishStep($newJetApplicant->applicant_id, 2);
        JetApplicantEnrollmentActions::startStep($newJetApplicant->applicant_id, 3);
        if ($newJetApplicant->scoober_id) {
            JetApplicantEnrollmentActions::finishStep($newJetApplicant->applicant_id, 15, true);
        }

        return $newJetApplicant;
    }

    public static function syncNewApplicants () {
        $jetService = new JetService;
        $fromDate = JetApplicant::select('lead_id', 'pre_contract_created_date')
            ->whereRaw('is_import IS NOT true')
            ->orderByDesc('pre_contract_created_date')
            ->value('pre_contract_created_date')
        ;

        $leadIdsToExclude = null;

        if ($fromDate) {
            $fromDate = Carbon::parse($fromDate)->toDateString();
            $leadIdsToExclude = JetApplicant::select('lead_id', 'pre_contract_created_date')
                ->whereRaw('is_import IS NOT true')
                ->where('pre_contract_created_date', '>=', $fromDate)
                ->pluck('lead_id')
                ->toArray()
            ;
        }

        $newApplicants = $jetService->fetchApplicantsFromReport($fromDate, $leadIdsToExclude);

        foreach ($newApplicants as $newApplicant) {
            try {
                self::syncNewApplicant($newApplicant);
            } catch (\Exception $exception) {
                report($exception);
                continue;
            }
        }
    }

    private static function onboardNewApplicant (JetApplicant $newApplicant) {
        $newApplicantDateOfBirth = Carbon::parse($newApplicant->date_of_birth);
        $newApplicantAge = $newApplicantDateOfBirth->diffInYears(Carbon::now());
        if ($newApplicantAge < 16) {
            $newApplicantDateOfBirthFormatted = $newApplicantDateOfBirth->format('d/m/Y');
            self::rejectApplicant($newApplicant->applicant_id, "The applicant is under 16 years old. The provided date of birth is $newApplicantDateOfBirthFormatted.");
            return;
        }

        [$newApplicantCountryCode, $newApplicantPhoneNumber] = self::explodePhone($newApplicant->mobile_phone) ?? self::explodePhone($newApplicant->phone);
        if (!$newApplicantCountryCode || !$newApplicantPhoneNumber) {
            $holdReason = ($newApplicant->mobile_phone) ?
                ('The applicant provided phone numbers (' . $newApplicant->phone . ' and ' . $newApplicant->mobile_phone . ') are not valid.') :
                ('The applicant provided phone number (' . $newApplicant->phone . ') is not valid.')
            ;
            self::holdApplicant($newApplicant->applicant_id, $holdReason);
            return;
        }

        $checkUserResponse = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
            ])
            ->withToken(config('services.fusionauth.key_check_user'), null)
            ->baseUrl(config('services.fusionauth.url'))
            ->get('/api/user', ['loginId' => strtolower($newApplicant->email)])
        ;
        $checkUserStatus = $checkUserResponse->status();

        if ($checkUserStatus === 404) { // If the user email was not previously registered:
            $userService = new UserService;
            $responseJsonArray = $userService->createUser(
                [
                    'email' => $newApplicant->email,
                    'first_name' => $newApplicant->first_name,
                    'voornaam' => $newApplicant->first_name,
                    'roles' => ['payroll'],
                    'phone_number' => "$newApplicantPhoneNumber",
                    'country_code' => "$newApplicantCountryCode"
                ],
                [
                    [ 'data' => [
                        'type' => 'address',
                        'attributes' => [
                            'street' => $newApplicant->street,
                            'house_number' => $newApplicant->house_number,
                            'house_number_addition' => $newApplicant->house_number_addition,
                            'postal_code' => $newApplicant->postcode,
                            'city' => $newApplicant->city
                        ]
                    ]],
                    [ 'data' => [
                        'type' => 'personaldata',
                        'attributes' => [
                            'first_name' => $newApplicant->first_name,
                            'last_name' => $newApplicant->last_name,
                            'phone_number' => "$newApplicantPhoneNumber",
                            'country_code' => "$newApplicantCountryCode",
                            'date_of_birth' => Carbon::parse($newApplicant->date_of_birth)->format('d-m-Y'),
                            'gender' => $newApplicant->gender === 'Male' ? 'man' : 'vrouw',
                            'emergency_phone_number' => $newApplicant->emergency_contact_phone
                        ]
                    ]],
                    [ 'data' => [
                        'type' => 'applicationprofile',
                        'attributes' => [
                            'language' => ($newApplicant->preferred_language === 'nl_NL') ? 'Nederlands' : 'Engels',
                        ]
                    ]],
                    [ 'data' => [
                        'type' => 'info',
                        'attributes' => [
                            'start_date' => $newApplicant->pre_contract_start_date,
                            'minimum_contract_hours' => $newApplicant->pre_contract_minimum_hours,
                            'driving_license_required' => self::isDrivingLicenseRequired($newApplicant->active_transportation_type),
                            'whatsapp_consent' => $newApplicant->whatsapp_consent,
                            'origin' => 'jet',
                            'final_information_check' => null
                        ]
                    ]],
                    [ 'data' => [
                        'type' => 'successiveemployership',
                        'attributes' => [
                            'successive_employership' => $newApplicant->previous_experience,
                            'job_title' => $newApplicant->pre_contract_function,
                            'number_of_contracts' => $newApplicant->number_of_previous_contracts,
                            'number_of_months' => $newApplicant->months_of_work_experience
                        ]
                    ]],
                    [ 'data' => [
                        'type' => 'identification',
                        'attributes' => [
                            'nationality' => (self::$alpha3IsoCodes[$newApplicant->country_of_nationality] ?? null)
                        ]
                    ]]
                ]
            );
            $newApplicant->flexapp_id = $responseJsonArray['data']['id'];
            $newApplicant->save();

            JetApplicantEnrollmentActions::finishStep($newApplicant->applicant_id, 3);
            JetApplicantEnrollmentActions::startStep($newApplicant->applicant_id, 4);
            return;
        }

        if ($checkUserStatus === 200) { // If the user email was previously registered:
            $existingWenoteApplicant = WenoteUser::where('emailadres', $newApplicant->email)->firstOrFail();
            $newApplicant->wenote_id = $existingWenoteApplicant->UKT_id;
            $newApplicant->flexapp_id = $existingWenoteApplicant->flex_id;
            $newApplicant->save();

            $isActiveInAnotherWkm = EasyflexUser::where('UKT_id', $existingWenoteApplicant->UKT_id)->pluck('WKM_id')->diff([7])->isNotEmpty();
            $userService = new UserService;

            if ($isActiveInAnotherWkm) {
                $userService->updateUserRole($newApplicant->flexapp_id, 'uitzendkracht');
                $userService->updateUser($newApplicant->flexapp_id, ['info' => [
                    'origin' => 'jet_plus'
                ]]);
                $existingWenoteApplicant->is_justeat = 0;
            } else {
                $userService->updateUserRole($newApplicant->flexapp_id, 'payroll');
                $userService->updateUser($newApplicant->flexapp_id, ['info' => [
                    'origin' => 'jet'
                ]]);
                $existingWenoteApplicant->is_justeat = 1;
            }
            $existingWenoteApplicant->ready_for_jobs = 1;
            $existingWenoteApplicant->save();

            $userService->updateUser($newApplicant->flexapp_id, [
                'personal_data' => [
                    'first_name' => $newApplicant->first_name,
                    'last_name' => $newApplicant->last_name,
                    'phone_number' => "$newApplicantPhoneNumber",
                    'country_code' => "$newApplicantCountryCode",
                    'date_of_birth' => Carbon::parse($newApplicant->date_of_birth)->format('d-m-Y'),
                    'gender' => $newApplicant->gender === 'Male' ? 'man' : 'vrouw',
                    'emergency_phone_number' => $newApplicant->emergency_contact_phone,
                ],
                'address' => [
                    'street' => $newApplicant->street,
                    'house_number' => $newApplicant->house_number,
                    'house_number_addition' => $newApplicant->house_number_addition,
                    'postal_code' => $newApplicant->postcode,
                    'city' => $newApplicant->city
                ],
                'application_profile' => [
                    'language' => ($newApplicant->preferred_language === 'nl_NL') ? 'Nederlands' : 'Engels',
                ],
                'info' => [
                    'start_date' => $newApplicant->pre_contract_start_date,
                    'end_date' => null,
                    'minimum_contract_hours' => $newApplicant->pre_contract_minimum_hours,
                    'driving_license_required' => self::isDrivingLicenseRequired($newApplicant->active_transportation_type),
                    'whatsapp_consent' => $newApplicant->whatsapp_consent,
                    'final_information_check' => null,
                    'final_contract_check' => null,
                    'has_finalize_profile' => null,
                    'twv_requested' => null,
                    'twv_requested_date' => null,
                    'twv_approved' => null,
                    'twv_approved_date' => null,
                    'twv_start_date' => null,
                    'twv_expiration_date' => null,
                    'uwv_notified' => null,
                    'uwv_notified_date' => null,
                    'right_to_work' => null
                ],
                'successive_employership' => [
                    'successive_employership' => true,
                    'job_title' => null,
                    'number_of_contracts' => null,
                    'number_of_months' => null
                ],
                'identification' => [
                    'nationality' => (self::$alpha3IsoCodes[$newApplicant->country_of_nationality] ?? null)
                ],
                'financial_details' => [
                    'iban' => null
                ]
            ]);
            $newApplicant->employeeOffboarding()->delete();
            $userService->sendJETRegistrationReminder($newApplicant->flexapp_id);

            JetApplicantEnrollmentActions::finishStep($newApplicant->applicant_id, 3);
            JetApplicantEnrollmentActions::finishStep($newApplicant->applicant_id, 4);
            JetApplicantEnrollmentActions::startStep($newApplicant->applicant_id, 5);
            return;
        }

        $checkUserResponse->throw();
    }

    public static function onboardNewApplicants () {
        $newApplicants = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
                $query
                    ->where('step_in_process', 3)
                    ->where('agency_status', 'New')
                    ->whereRaw('on_hold IS NOT true')
                ;
            })
            ->orderBy('pre_contract_created_date')
            ->get()
        ;

        foreach ($newApplicants as $newApplicant) {
            try {
                self::onboardNewApplicant($newApplicant);
            } catch (\Exception $exception) {
                report($exception);
                continue;
            }
        }
    }

    public static function updateApplicantsWenoteId () {
        $applicants = JetApplicant::whereNull('wenote_id')
            ->whereNotNull('flexapp_id')
            ->get()
        ;
        foreach ($applicants as $applicant) {
            $applicant->wenote_id = $applicant->wenoteData->UKT_id;
            $applicant->save();
        }
    }

    public static function updateApplicantsStepInProgress (int $stepInProgress) {
        if ($stepInProgress === 3) {
            $step3Applicants = JetApplicant::whereHas('enrollmentStatus', function (Builder $query) {
                $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where('step_in_process', 3)
                    ->whereNotNull('step_3_started_at')
                    ->whereNull('step_3_finished_at')
                ;
            })->get();
            foreach ($step3Applicants as $step3Applicant) {
                try {
                    if ($step3Applicant->flexappData->info->final_information_check) {
                        self::timeApplicantEnrollmentStep($step3Applicant->applicant_id, 3, true);
                        self::timeApplicantEnrollmentStep($step3Applicant->applicant_id, 4);
                    }
                } catch (\Exception $exception) {
                    report($exception);
                    continue;
                }
            }
        }

        if ($stepInProgress === 6) {
            $step6Applicants = JetApplicant::whereNotNull('wenote_id')
                ->whereHas('enrollmentStatus', function (Builder $query) {
                    $query->whereRaw('on_hold IS NOT true')
                        ->where('step_in_process', '=', 6)
                        ->where('agency_status', '!=', 'Rejected')
                        ->whereNotNull('step_6_started_at')
                        ->whereNull('step_6_finished_at');
                })
                ->get();
            ;
            foreach ($step6Applicants as $step6Applicant) {
                try {
                    $response = Http::withHeaders([
                        'Accept' => 'application/vnd.api+json',
                        'Content-Type' => 'application/vnd.api+json',
                    ])
                        ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.signhost_service')))
                        ->baseUrl(config('services.signhost_service.url'))
                        ->throw()
                        ->get('/get_jet_contract_status/' . $step6Applicant->flexapp_id);

                    $responseJson = $response->json();
                    if($responseJson['contracts_signed'] === true){
                        $step6FinishedAt = self::timeApplicantEnrollmentStep($step6Applicant->applicant_id, 6, true);
                        $step7StartedAt = self::timeApplicantEnrollmentStep($step6Applicant->applicant_id, 7);
                        JetApplicantEnrollmentActions::finishStep($step6Applicant->applicant_id, 12, false, $step6FinishedAt);
                        JetApplicantEnrollmentActions::startStep($step6Applicant->applicant_id, 13, false, $step7StartedAt);
                    }

                } catch (\Exception $exception) {
                    report($exception->getMessage());
                    continue;
                }
            }
        }

        if ($stepInProgress === 7) {
            $step7Applicants = JetApplicant::whereHas('enrollmentStatus', function (Builder $query) {
                $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where('step_in_process', 7)
                    ->whereNotNull('step_7_started_at')
                    ->whereNull('step_7_finished_at')
                ;
            })->get();
            foreach ($step7Applicants as $step7Applicant) {
                try {
                    self::approveApplicant($step7Applicant->applicant_id);

                    $step7FinishedAt = self::timeApplicantEnrollmentStep($step7Applicant->applicant_id, 7, true);
                    $step8StartedAt = self::timeApplicantEnrollmentStep($step7Applicant->applicant_id, 8);
                    JetApplicantEnrollmentActions::finishStep($step7Applicant->applicant_id, 13, false, $step7FinishedAt);
                    JetApplicantEnrollmentActions::startStep($step7Applicant->applicant_id, 14, false, $step8StartedAt);
                    if (!$step7Applicant->scoober_id) {
                        $step9StartedAt = self::timeApplicantEnrollmentStep($step7Applicant->applicant_id, 9, false, true);
                        JetApplicantEnrollmentActions::startStep($step7Applicant->applicant_id, 15, true, $step9StartedAt);
                    }
                } catch (\Exception $exception) {
                    report($exception);
                    continue;
                }
            }
        }

        if ($stepInProgress === 9) {
            $jetService = new JetService;
            $step9Applicants = JetApplicant::whereNull('scoober_id')
                ->whereRaw('is_import IS NOT true')
                ->whereHas('enrollmentStatus', function (Builder $query) {
                    $query
                        ->whereRaw('on_hold IS NOT true')
                        ->whereNotNull('step_9_started_at')
                        ->whereNull('step_9_finished_at')
                    ;}
                )
                ->get()
            ;
            foreach ($step9Applicants as $step9Applicant) {
                try {
                    $step9ApplicantDTO = $jetService->fetchApplicant($step9Applicant->lead_id);
                    if ($step9ApplicantDTO->contact_id && $step9ApplicantDTO->scoober_id) {
                        $step9Applicant->contact_id = $step9ApplicantDTO->contact_id;
                        $step9Applicant->scoober_id = $step9ApplicantDTO->scoober_id;
                        $step9Applicant->save();
                        $step9Applicant->flexappData->info->scoober_id = $step9ApplicantDTO->scoober_id;
                        $step9Applicant->flexappData->info->save();
                        $step9FinishedAt = self::timeApplicantEnrollmentStep($step9Applicant->applicant_id, 9, true, true);
                        JetApplicantEnrollmentActions::finishStep($step9Applicant->applicant_id, 15, true, $step9FinishedAt);
                    }
                } catch (\Exception $exception) {
                    report($exception);
                    continue;
                }
            }
        }
    }

    public static function approveApplicant (int $applicantId, string $reason = null) {
        $applicant = JetApplicant::findOrFail($applicantId);
        $jetService = new JetService;

        #region Update preContract
        $preContractStartDate = $applicant->flexappData->info->start_date;
        $preContractEndDate = $applicant->flexappData->info->end_date;
        $isStudent = (bool)$applicant->flexappData->info->twv_is_student;
        $preContractMinimumHours = (int)$applicant->flexappData->info->minimum_contract_hours;


        if (!$applicant->is_import) {
            $jetService->updatePreContractDates($applicant->pre_contract_id, $preContractStartDate, $preContractEndDate);
            $jetService->updateApplicantIsStudent($applicant->lead_id, $isStudent);
            $jetService->updatePreContractType($applicant->pre_contract_id, $preContractMinimumHours, $isStudent);
        }

        $applicant->pre_contract_start_date = $preContractStartDate;
        $applicant->pre_contract_end_date = $preContractEndDate;
        $applicant->is_student = $isStudent;
        $applicant->pre_contract_minimum_hours = $preContractMinimumHours;
        $applicant->save();
        #endregion

        if (!$applicant->is_import) {
            if ($applicant->flexappData->identification->document_type === 'Verblijfsvergunning') {
                try {
                    SaveResidencePermit::forApplicant($applicant);
                } catch (\Throwable $e) {
                    Log::error('error with processing the residence permit for applicant', [
                        'flexapp_id' => $applicant->flexapp_id,
                        'message' => $e->getMessage(),
                    ]);
                }
            }

            $jetService->updateApplicantAgencyStatus($applicant->lead_id, 'Approved');
        }

        $agencyStatusUpdatedAt = Carbon::now()->toDateTimeString();
        if ($applicant->enrollmentStatus) {
            $applicant->enrollmentStatus->agency_status = 'Approved';
            $applicant->enrollmentStatus->agency_status_reason = $reason;
            $applicant->enrollmentStatus->agency_status_updated_at = $agencyStatusUpdatedAt;
            $applicant->enrollmentStatus->save();
        }
        #region New
        $applicant->enrollmentStatusNew->agency_status = 'Approved';
        $applicant->enrollmentStatusNew->agency_status_reason = $reason;
        $applicant->enrollmentStatusNew->agency_status_updated_at = $agencyStatusUpdatedAt;
        $applicant->enrollmentStatusNew->save();
        #endregion
    }

    public static function rejectApplicant (int $applicantId, string $reason) {
        if (strlen($reason) > 255) {
            throw new \Exception('The reason length is not valid.', 400);
        }
        $applicant = JetApplicant::findOrFail($applicantId);

        if (!$applicant->is_import) {
            $jetService = new JetService;
            $jetService->updateApplicantAgencyStatus($applicant->lead_id, 'Rejected', $reason);
        }

        $agencyStatusUpdatedAt = Carbon::now()->toDateTimeString();
        if ($applicant->enrollmentStatus) {
            $applicant->enrollmentStatus->agency_status = 'Rejected';
            $applicant->enrollmentStatus->agency_status_reason = $reason;
            $applicant->enrollmentStatus->agency_status_updated_at = $agencyStatusUpdatedAt;
            $applicant->enrollmentStatus->save();
        }
        #region New
        $applicant->enrollmentStatusNew->agency_status = 'Rejected';
        $applicant->enrollmentStatusNew->agency_status_reason = $reason;
        $applicant->enrollmentStatusNew->agency_status_updated_at = $agencyStatusUpdatedAt;
        $applicant->enrollmentStatusNew->save();
        #endregion
    }

    public static function revertRejectedApplicant (int $applicantId) {
        $applicant = JetApplicant::findOrFail($applicantId);

        if (!$applicant->is_import) {
            $jetService = new JetService;
            $jetService->updateApplicantAgencyStatus($applicant->lead_id, 'New');
        }

        $agencyStatusUpdatedAt = Carbon::now()->toDateTimeString();
        if ($applicant->enrollmentStatus) {
            $applicant->enrollmentStatus->agency_status = 'New';
            $applicant->enrollmentStatus->agency_status_reason = null;
            $applicant->enrollmentStatus->agency_status_updated_at = $agencyStatusUpdatedAt;
            $applicant->enrollmentStatus->save();
        }
        #region New
        $applicant->enrollmentStatusNew->agency_status = 'New';
        $applicant->enrollmentStatusNew->agency_status_reason = null;
        $applicant->enrollmentStatusNew->agency_status_updated_at = $agencyStatusUpdatedAt;
        $applicant->enrollmentStatusNew->save();
        #endregion
    }

    public static function holdApplicant (int $applicantId, string $reason) {
        if (strlen($reason) > 255) {
            throw new \Exception('The reason length is not valid.', 400);
        }
        $applicant = JetApplicant::findOrFail($applicantId);

        if (!$applicant->enrollmentStatusNew->on_hold) {
            $onHoldStartedAt = Carbon::now()->toDateTimeString();
            if ($applicant->enrollmentStatus) {
                $applicant->enrollmentStatus->on_hold = true;
                $applicant->enrollmentStatus->on_hold_reason = $reason;
                $applicant->enrollmentStatus->on_hold_started_at = $onHoldStartedAt;
                $applicant->enrollmentStatus->on_hold_finished_at = null;
                $applicant->enrollmentStatus->save();
            }
            #region New
            $applicant->enrollmentStatusNew->on_hold = true;
            $applicant->enrollmentStatusNew->on_hold_reason = $reason;
            $applicant->enrollmentStatusNew->on_hold_started_at = $onHoldStartedAt;
            $applicant->enrollmentStatusNew->on_hold_finished_at = null;
            $applicant->enrollmentStatusNew->save();
            #endregion
        }
    }

    public static function releaseApplicant (int $applicantId) {
        $applicant = JetApplicant::findOrFail($applicantId);

        if ($applicant->enrollmentStatusNew->on_hold) {
            $onHoldFinishedAt = Carbon::now()->toDateTimeString();
            if ($applicant->enrollmentStatus) {
                $applicant->enrollmentStatus->on_hold = false;
                $applicant->enrollmentStatus->on_hold_finished_at = $onHoldFinishedAt;
                $applicant->enrollmentStatus->save();
            }
            #region New
            $applicant->enrollmentStatusNew->on_hold = false;
            $applicant->enrollmentStatusNew->on_hold_finished_at = $onHoldFinishedAt;
            $applicant->enrollmentStatusNew->save();
            #endregion
        }
    }

    public static function syncApplicantsDrivingLicense () {
        $applicants = JetApplicant::whereHas('enrollmentStatus', function (Builder $query) {
                $query
                    ->whereRaw('on_hold IS NOT true')
                    ->whereNotNull('step_4_started_at')
                    ->whereNotNull('step_4_finished_at')
                ;
            })
            ->whereIn('active_transportation_type', ['Motorcycle / Scooter', 'E-Roller', 'Car / Kombi', 'Buffer Vehicle'])
            ->whereNull('driving_license_number')
            ->whereNull('driving_license_expiration_date')
            ->get()
        ;

        foreach ($applicants as $applicant) {
            try {
                if (
                    $applicant->flexappData->drivingLicense &&
                    $applicant->flexappData->drivingLicense->date_of_expiry &&
                    $applicant->flexappData->drivingLicense->document_number
                ) {
                    if (!$applicant->is_import) {
                        $jetService = new JetService;
                        $jetService->updateApplicantDrivingLicense($applicant->lead_id, $applicant->flexappData->drivingLicense->document_number, $applicant->flexappData->drivingLicense->date_of_expiry);
                    }

                    $applicant->driving_license_expiration_date = $applicant->flexappData->drivingLicense->date_of_expiry;
                    $applicant->driving_license_number = $applicant->flexappData->drivingLicense->document_number;
                    $applicant->save();
                }
            } catch (\Exception $exception) {
                report($exception);
                continue;
            }
        }
    }
    #endregion
}
