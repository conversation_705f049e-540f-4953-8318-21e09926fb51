# Samenvatting
Het doel van de wijzigingen is om een overzicht te maken van bedrijfskritieke processen.

Dit houd in dat we een overzicht willen hebben van de volgende processen:
- verblijfsvergunningen
- signhost transactions
- signhost postback
- signhost api calls

Van uit de signhost-service krijgen we deze data via een API request binnen, deze data wordt vervolgens opgeslagen in Sushi Eloquent models.
Deze data wordt momenteel nog niet permanent opgeslagen, maar dit kan met Cache gedaan worden. Dit is momenteel echter, vanwege het testen, nog niet gebeurd.

Mijn inziens kan dit in de `getRows()` methode worden gedaan, met een TTL van bijvoorbeeld vijf minuten.
Dan wordt er binnen de request de cache gebruikt, en blijft de data ook redelijk recent.

## Aanpak
Voor de verblijfsvergunningen
