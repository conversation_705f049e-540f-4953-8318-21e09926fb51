parameters:
	ignoreErrors:
		-
			message: '#^Call to an undefined static method App\\Models\\Export\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ImportResource.php

		-
			message: '#^Access to an undefined property App\\Filament\\Resources\\ImportResource\\Pages\\Arbeidstijdenwet\:\:\$applicant\.$#'
			identifier: property.notFound
			count: 1
			path: app/Filament/Resources/ImportResource/Pages/Arbeidstijdenwet.php

		-
			message: '#^Access to an undefined property App\\Filament\\Resources\\ImportResource\\Pages\\Arbeidstijdenwet\:\:\$date\.$#'
			identifier: property.notFound
			count: 4
			path: app/Filament/Resources/ImportResource/Pages/Arbeidstijdenwet.php

		-
			message: '#^Call to an undefined static method App\\Models\\Flexapp\\User\:\:whereIn\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ImportResource/Pages/Arbeidstijdenwet.php

		-
			message: '#^Call to an undefined static method App\\Models\\Urenbestand\:\:join\(\)\.$#'
			identifier: staticMethod.notFound
			count: 2
			path: app/Filament/Resources/ImportResource/Pages/Arbeidstijdenwet.php

		-
			message: '#^Call to an undefined static method App\\Models\\Urenbestand\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 3
			path: app/Filament/Resources/ImportResource/Pages/Arbeidstijdenwet.php

		-
			message: '#^Call to an undefined static method App\\Models\\Export\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 3
			path: app/Filament/Resources/ImportResource/Pages/EditImport.php

		-
			message: '#^Call to an undefined static method App\\Models\\LoonComponent\:\:find\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ImportResource/Pages/EditImport.php

		-
			message: '#^Call to an undefined static method App\\Models\\Urenbestand\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ImportResource/Pages/EditImport.php

		-
			message: '#^Call to an undefined static method Filament\\Actions\\Imports\\Models\\Import\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ImportResource/Pages/EditImport.php

		-
			message: '#^Anonymous function has an unused use \$enrollmentSteps\.$#'
			identifier: closure.unusedUse
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicantEnrollmentStatusNew\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 2
			path: app/Filament/Resources/JetApplicantNewResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\Flexapp\\UserInfo\:\:whereIn\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ListNewJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ListNewJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\EmployeeOffboarding\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetEnrollmentStep\:\:find\(\)\.$#'
			identifier: staticMethod.notFound
			count: 2
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\LooncomponentPercentage\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\TwvInformation\:\:create\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\WeeklyHours\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantNewResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\Flexapp\\UserInfo\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantResource/Pages/ListNewJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\EmployeeOffboarding\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\LooncomponentPercentage\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\TwvInformation\:\:create\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\WeeklyHours\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/JetApplicantResource/Pages/ViewCheckJetApplicants.php

		-
			message: '#^Call to an undefined static method App\\Models\\LooncomponentPercentage\:\:select\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/LoonComponent/LooncomponentPercentageResource/Pages/ListLooncomponentPercentages.php

		-
			message: '#^Call to an undefined static method App\\Models\\DatacheckerTransaction\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:select\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\ResidencePermitRenewal\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\TwvInformation\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource.php

		-
			message: '#^Variable \$data on left side of \?\? is never defined\.$#'
			identifier: nullCoalesce.variable
			count: 1
			path: app/Filament/Resources/ResidencePermitResource.php

		-
			message: '#^Anonymous function has an unused use \$flexappIds\.$#'
			identifier: closure.unusedUse
			count: 1
			path: app/Filament/Resources/ResidencePermitResource/Pages/ListResidencePermits.php

		-
			message: '#^Call to an undefined static method App\\Models\\DatacheckerTransaction\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource/Pages/ListResidencePermits.php

		-
			message: '#^Call to an undefined static method App\\Models\\DatacheckerTransaction\:\:whereIn\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource/Pages/ListResidencePermits.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:select\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/ResidencePermitResource/Pages/ListResidencePermits.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:whereHas\(\)\.$#'
			identifier: staticMethod.notFound
			count: 3
			path: app/Filament/Resources/ResidencePermitResource/Pages/ListResidencePermits.php

		-
			message: '#^Class App\\Filament\\Resources\\Shield\\RoleResource\\Pages\\CreateRole has an uninitialized property \$permissions\. Give it default value or assign it in the constructor\.$#'
			identifier: property.uninitialized
			count: 1
			path: app/Filament/Resources/Shield/RoleResource/Pages/CreateRole.php

		-
			message: '#^Class App\\Filament\\Resources\\Shield\\RoleResource\\Pages\\EditRole has an uninitialized property \$permissions\. Give it default value or assign it in the constructor\.$#'
			identifier: property.uninitialized
			count: 1
			path: app/Filament/Resources/Shield/RoleResource/Pages/EditRole.php

		-
			message: '#^Call to an undefined static method App\\Models\\DatacheckerTransaction\:\:selectRaw\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/StickerResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\DatacheckerTransaction\:\:whereIn\(\)\.$#'
			identifier: staticMethod.notFound
			count: 2
			path: app/Filament/Resources/StickerResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:whereHas\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/StickerResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\Flexapp\\User\:\:whereHas\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/TwvResource.php

		-
			message: '#^Call to an undefined static method App\\Models\\JetApplicant\:\:select\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Filament/Resources/TwvResource.php

		-
			message: '#^Access to an undefined property App\\Models\\Export\:\:\$import_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/Export.php

		-
			message: '#^Call to an undefined static method App\\Models\\Export\:\:hasPolymorphicUserRelationship\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Models/Export.php

		-
			message: '#^Access to an undefined property App\\Models\\ImportExtended\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ImportExtended.php

		-
			message: '#^Call to an undefined static method App\\Models\\Urenbestand\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Models/ImportExtended.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$email\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to an undefined static method App\\Models\\WeeklyHours\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Models/WeeklyHours.php
